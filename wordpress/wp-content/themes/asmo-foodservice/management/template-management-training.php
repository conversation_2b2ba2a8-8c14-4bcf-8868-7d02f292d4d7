<?php
/*
 * Template Name: Management Trannings Template
 */
?>
</style>
<?php get_header(); ?>
<div class="title_area clearfix">
	<div class="title_areain clearfix">
		<div class="title01">
			<h2><?php the_title(); ?></h2>
		</div>
		<div class="common_logo">
			<img src="<?php echo  get_template_directory_uri() . '/images/recruit_bnr.png' ?>" alt="株式会社アスモフードサービス" />
		</div>
	</div>
</div>
<?php
$current_uri = trailingslashit(home_url($_SERVER['REQUEST_URI']));
$args = [
	'post_type'  => 'page',
	'meta_key'   => '_wp_page_template',
	'meta_value' => 'management/template-management-training.php',
	'post_status' => 'publish',
	'posts_per_page' => -1,
	'orderby' => 'date',
	'order'   => 'desc',
];

$query = new WP_Query($args);

?>
<div class="wrapper management_wrapper">
	<div class="management-content">
		<!-- <div class="management-tab">
			<?php
			$order_titles = [
				'人材育成',
				'新卒研修',
				'従業員研修',
				'料理コンテスト',
				'洋菓子講習会'
			];
			$posts = [];
			if ($query->have_posts()) {
				while ($query->have_posts()) {
					$query->the_post();
					$posts[get_the_title()] = [
						'link' => trailingslashit(get_permalink()),
						'title' => get_the_title()
					];
				}
				wp_reset_postdata();
				foreach ($order_titles as $title) {
					if (isset($posts[$title])) {
						$active = ($current_uri == $posts[$title]['link']) ? 'active' : '';
						echo '<a href="' . esc_url($posts[$title]['link']) . '" class="item-tab ' . esc_attr($active) . '">' . esc_html($title) . '</a>';
					}
				}
			}
			?>
		</div> -->
		<div class="management-tab">
			<?php if ($query->have_posts()) {
				while ($query->have_posts()) {
					$query->the_post();
					$active = '';
					if ($current_uri == get_permalink()) {
						$active = 'active';
					}
					echo '<a href="'. get_permalink() .'" class="item-tab '. $active .'">'. get_the_title() . '</a>';
				}
				wp_reset_postdata();
			}?>
		</div>
	</div>

	<?php if (have_posts()): ?>
		<?php while (have_posts()): the_post(); ?>
			<?php the_content(); ?>
		<?php endwhile; ?>
	<?php endif; ?>
</div>
</div><!-- wrapper end -->
<?php get_footer(); ?>

<script>
	$('.js-toggle-model').on('click', function() {
		$('.model-list').toggleClass('active')
	})
</script>

<style>

</style>