/* management page*/
.management_wrapper{
    padding-bottom: 50px;
     background-color: rgb(172, 232, 218, .35);
}
.management_wrapper+.contact_area{
    margin: 0;
    background-color: rgb(172, 232, 218, .35)
}
.management_wrapper .title-row-3-sub{
    text-align: center;
    margin-bottom: 30px;
    padding: 10px 0 10px 0;
    color: #222222;
}
.management_wrapper .title-row-3-sub h4{
    font-size: 21px;
}
.management_wrapper .management-content {
    margin: auto;
    padding: 0 10px;
    max-width: 1220px;
}
/* tab */
.management_wrapper .management-content .management-tab{
    margin: 100px auto 75px auto;
    display: flex;
    max-width: 930px;
    height: 68px;
    border: 1px solid #00b67f;
    border-radius: 20px;
    overflow: hidden;
}
.management_wrapper .management-content .management-tab .item-tab{
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 25%;
    font-size: 18px;
    font-weight: 700;
    color: #00b67f;
    border-right: 1px solid #00b67f;
    background-color: #FFF;
}
.management_wrapper .management-content .management-tab .item-tab:last-child{
    border-right: unset;
}
.management_wrapper .management-content .management-tab .item-tab.active{
    background: #00b67f;
    color: #FFF;
}
@media only screen and (max-width: 600px) {
    .management_wrapper .management-content .management-tab{
        margin: 80px auto 55px auto;
        height: 58px;
    }
    .management_wrapper .management-content .management-tab .item-tab{
        font-size: 16px;
    }
}
@media only screen and (max-width: 425px) {
    .management_wrapper .management-content .management-tab{
        height: 40px;
    }
    .management_wrapper .management-content .management-tab .item-tab{
        font-size: 12px;
    }
}
/* end home */
/* home */
.management_wrapper .management-content .home .list-image{
    display: flex;
    justify-content: center;
}
.management_wrapper .management-content .home .list-image .img{
    margin: 0 15px;
    width: 280px;
    height: 245px;
}
.management_wrapper .management-content .home .list-image .img img{
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.management_wrapper .management-content .home .decs{
    padding-top: 50px;
    font-size: 20px;
    text-align: center;
}

@media only screen and (max-width: 600px) {
    .management_wrapper .management-content .home .list-image{
        flex-direction: column;
        align-items: center;
    }
    .management_wrapper .management-content .home .list-image .img{
        margin: 15px;
        width: 90%;
        height: unset;
    }
    .management_wrapper .management-content .home .list-image .img img{
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    .management_wrapper .management-content .home .decs{
        font-size: 16px;
    }
}
/* end home */
/* emloyer */
.management_wrapper .management-content .emloyer{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.management_wrapper .management-content .emloyer .image{
    /* margin-bottom: 35px; */
}
.management_wrapper .management-content .emloyer .image img{
    width: 100%;
    object-fit: fill;
}
.management_wrapper .management-content .emloyer .title{
    margin-top: 85px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 22px;
    font-weight: 700;
}
/* end emloyer */
/* schedule */
.management_wrapper .management-content .schedule{
    margin: auto;
    max-width: 1024px;
}
.management_wrapper .management-content .schedule .decs{
    padding-bottom: 25px;
    font-size: 20px;
}
.management_wrapper .management-content .schedule .title{
    margin: 35px 0 25px 0;
    text-align: center;
    font-size: 22px;
    font-weight: bold;
}
.management_wrapper .management-content .schedule .image{
    display: flex;
}
.management_wrapper .management-content .schedule .image img{
    margin: auto;
    /* border-radius: 35px; */
    /* overflow: hidden; */
}
.management_wrapper .management-content .schedule .info{
    padding: 25px 10px;
    background-color: #C2F1C8;
    text-align: center;
    font-size: 18px;
    line-height: 1.8;
}
/* end schedule */
/* application */
.management_wrapper .management-content .application{
    margin: auto;
    max-width: 1024px;
    width: 100%;
}
.management_wrapper .management-content .application .item-app{
    margin: 0 auto 25px 0;
}
.management_wrapper .management-content .application .item-app .title{
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: bold;
}
.management_wrapper .management-content .application .item-app .info-group{
    display: flex;
    justify-content: space-between;
}
.management_wrapper .management-content .application .item-app .info-group .image{
    display: inline-flex;
    width: 48%;
    height: 325px;
}
.management_wrapper .management-content .application .item-app .info-group .image img{
    width: 100%;
}
.management_wrapper .management-content .application .item-app .info-group .desc{
    padding: 15px 0;
    width: 50%;
    line-height: 1.6;
    font-size: 16px;
}
.management_wrapper .management-content .application .model-list{
    position: fixed;
    display: flex;
    width: 100vw;
    height: 100vh;
    left: 50%;
    top: 50%;
    transform: scale(0);
    background-color: #3434;
    z-index: 999999;
}
.management_wrapper .management-content .application .model-list .content{
    margin: auto;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    max-width: 1024px;
    width: 100%;    
    transition: transform 0.3s ease;
    transform-origin: center;
}
.management_wrapper .management-content .application .model-list.active{
    transform: translate(-50%, -50%) scale(1);
}
.management_wrapper .management-content .application .model-list .image{
    width: 48%;
}
.management_wrapper .management-content .application .model-list .image img{
    width: 100%;
}
@media only screen and (max-width: 768px) {
    .management_wrapper .management-content .application .item-app .info-group{
        flex-direction: column;
    }
    .management_wrapper .management-content .application .item-app .info-group .image{
        width: 100%;
        height: unset;
    }
    .management_wrapper .management-content .application .item-app .info-group .desc{
        width: 100%;
        font-size: 14px;
    }
    .management_wrapper .management-content .application .model-list .content {
        flex-direction: column;
    }
    .management_wrapper .management-content .application .model-list .content .image{
        margin-bottom: 15px;
        width: 100%;
    }
}
/* end application */
/*end of management page*/


