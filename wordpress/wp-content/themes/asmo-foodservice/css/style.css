@charset "utf-8";

/*===============================================
●style.css 画面の横幅が641px以上
===============================================*/
.g-recaptcha{
	margin-bottom: 15px; 
	display: flex; 
	justify-content: center;
}
table{
    border-collapse: collapse;
    border-spacing: 0;
}
.bg-white{
	background: #fff !important;
}
.bold{
    font-weight: bold;
}
.orange{
    color: #ef8d00;
}
.white{
    color: #fff;
}
.red{
    color: #b40000;
}
.h3_common{
	display: block;
	text-align: center;
	margin: 0 0 70px 0;
}
.h3_common h3{
	font-size: 30px;
	line-height: 1;
	font-weight: bold;
	color: #222222;
	display: inline-block;
	position: relative;
}
.h3_common h3:after{
	content: " ";
	position: absolute;
	background: #00b67f;
	width: 50px;
	height: 4px;
	left: 50%;
	bottom: -15px;
	transform: translate(-50%);
}
/*table01*/
.table01{
	width: 100%;
	margin:0 auto 20px auto;
}
.table01 th{
	width:160px;
	padding: 10px;
	text-align: center;
	vertical-align:top;
	font-weight:bold;
	background: #ededed;
	border-top:2px dotted #ccc;
	border-bottom:2px dotted #ccc;
	font-size: 16px;
	line-height: 1.5;
}
.table01 td{
	padding: 10px;
	background:#fff;
	border-top:2px dotted #ccc;
	border-bottom:2px dotted #ccc;
	line-height: 1.5;
	font-size: 16px;
	line-height: 1.5;
	vertical-align: top;
}
.table01 td table td{
	padding:0;
	border:none;
	line-height: 1.5;
}
.table01 .staff td{
	text-align:right;
	border: none;
	padding-right:20px;
}
/*end of table01*/

/*table03*/
.table03{
	width: 1020px;
	margin: 0 auto 20px auto;
}
.table03 th{
	padding: 12px;
	font-weight: bold;
	background: #eee;
	border: 1px solid #ddd;
	text-align: left;
	font-size: 16px;
	line-height: 1;
}
.table03 td{
	padding: 10px;
	border: 1px solid #ddd;
	width: 680px;
	font-size: 16px;
	line-height: 1;
}
.table03 td:last-child{
	width: 470px;
}
/*end of table03*/

.zoom{
	overflow: hidden;
}
.zoom img{
	transition: .8s;
}
.list-mv01{
	opacity: 0;
	transform: translate(0,150px);
	-webkit-transform: translate(0,150px);
}
.mv01{
	opacity: 1.0;
	transform: translate(0,0);
	-webkit-transform: translate(0,0);
}

.list-mv02{
	opacity: 0;
	transform: translate(-250px,0);
	-webkit-transform: translate(-250px,0);
}
.mv02{
	opacity: 1.0;
	transform: translate(0,0);
	-webkit-transform: translate(0,0);
}

.list-mv03{
	transform: scale(0, 0);
	-webkit-transform: scale(0, 0);
	transition: .8s;
}
.mv03{
	transform: scale(1, 1);
	-webkit-transform: scale(1, 1);
}

/***** index *****/
#container{
	min-width:100%;
	display: block;
}
.inner{
	max-width: 1220px;
	width: 100%;
	margin: 0 auto;
	background: #fff;
	padding: 40px;
}
.title_area{
	min-width: 100%;
	background: #ededed;
}
.title_areain{
	width: 100%;
	margin: 0 auto;
	padding: 0;
	vertical-align: middle;
	box-sizing: border-box;
}
.title_area .title01{
	width: 100%;
	max-width: 1220px;
	position: absolute;
	left: 50%;
	transform: translate(-50%);
	padding: 82px 0 82px 0;
}
.title_area .title01 h2{
	font-size: 28px;
	font-weight: bold;
	padding: 0 10px;
	color: #fff;
}
.title_area .title01 h2 span{
	display: block;
	font-size: 17px;
	font-weight: bold;
}
.title_area .common_logo{
	/* width: 60%; */
	/* float: left; */
	/* margin: 0 auto; */
}
.title_area .common_logo img{
	width: 100%;
	height: 740px;
	object-fit: cover;
}
.header{
	min-width:100%;
	position: fixed;
	z-index: 3000;
	background: #fff;
}
.headerin{
    max-width: 1220px;
    display: block;
    margin: 0 auto;
    border-bottom: 1px solid #e2e2e2;
}
.headerin .logo{
    float: left;
    margin-top: 10px;
    margin-left: 20px;
    margin-right: 30px;
    margin-bottom: 10px;
}
.headerin .logo img{
	width: 235px;
	height: auto;
}
.top_right{
	display: flex;
	float: right;
	padding-top: 35px;
	align-items: center;
    justify-content: flex-end;
}
.top_right .sitemap{
    border-right: 1px solid #e2e2e2;
    padding-right: 20px;
}
.top_right .sitemap,
.top_right .inquiry,
.top_right .fb,
.top_right .insta{
	display: inline-block;
	float: left;
	/* width: 110px; */
	height: auto;
	margin-right: 27px;
}
.top_right .insta{
	margin-right: 0;
}
.top_right img{
	width: 100%;
}
.top_right #onhover{
  display: none;
}
.top_right #group_img_change:hover img[id="initial"]{
  display: none;
}
.top_right #group_img_change:hover img[id="onhover"]{
  display: block;
}
.nav{
    min-width: 100%;
    position: fixed;
    margin-top: 89px;
    z-index: 3000;
    background: #fff;
}
.nav_bg{
	display: block;
	height: 145px;
}
.drawr{
    max-height: 45px !important;
}
.navin{
    max-width: 1220px;
    width: 97%;
    margin: 0 auto;
    display: block;
}
.dropmenu{
    *zoom: 1;
    padding-left: 10px;
    padding-right: 10px;
}
.dropmenu:before, .dropmenu:after{
	content: "";
	display: table;
}
.dropmenu:after{
	clear: both;
}
.dropmenu > li{
    position: relative;
    width: 25%;
    line-height: 1.3;
    float: left;
    text-align: center;
    border-right: 2px solid #f0f0f0;
    display: block;
}
.dropmenu li:first-child{
    border-left: 2px solid #f0f0f0;
}
.dropmenu li:last-child{
	border-right: 2px solid #f0f0f0;
}
#fade_in li ul{
    opacity: 0;
    top: 50%;
    display: none;
    width: 100%;
}
#fade_in li:hover ul{
	top: 0;
    display: block;
    opacity: 1;
    position: fixed;
    z-index: 1111;
    width: 100%;
    height: 416px;
    left: 50%;
    right: 0;
    margin-top: 145px;
    transform: translateX(-50%);
}

#fade_in li:first-child:hover ul{
    background: #f3d9d1a8;
}
#fade_in.dropmenu > li:first-child:hover, 
#fade_in li:first-child ul li{
    background: #f3d9d1;
}
#fade_in li:nth-child(2):hover ul{
    background: #d1f3eca8;
}
#fade_in.dropmenu > li:nth-child(2):hover, 
#fade_in li:nth-child(2) ul li{
    background: #d1f3ec;
}
#fade_in li:nth-child(3):hover ul{
    background: #d1ddf3a8;
}

#fade_in.dropmenu > li:nth-child(3):hover, 
#fade_in li:nth-child(3) ul li{
    background: #d1ddf3;
}
#fade_in li:nth-child(4):hover ul{
    background: #d1f3eca8;
}
#fade_in.dropmenu > li:nth-child(4):hover, 
#fade_in li:nth-child(4) ul li{
    background: #d1f3ec;
}

.dropmenu li a{
    display: block;
    color: #4B4B4B;
    font-size: 14px;
    font-weight: bold;
    text-decoration: none;
    padding: 13px 0;
}
.dropmenu li a:link{
    color: #4B4B4B;
}
.dropmenu li:hover{
    /* background: #FAF8FA; */
}
.dropmenu li ul{
    position: absolute;
    z-index: 9999;
    top: 100%;
    left: 0;
}
.dropmenu li ul li:first-child{
    width: 40%;
    height: 90%;
    padding: 12px 0 0 12px;
    /* background: #ffe1da; */
    border-left: 1px solid #fff;
}
.dropmenu li ul li{
    position: relative;
    line-height: 1.3;
    float: left;
    text-align: center;
    display: block;
    /* background: #ffe1da; */
    border-right: 1px solid #fff;
    border-bottom: 1px solid #fff;
    padding: 10px;
    width: 200px;
    position: relative;
}
.dropmenu li ul li:first-child a{
	font-size: 24px;
    text-align: left;
    margin-left: 24px;
}

.dropmenu li ul li:not(:first-child) a{
    /* background: #FAF8FA; */
    color: #4B4B4B;
    text-align: left;
    font-size: 13px;
    display: flex;
    flex-direction: column;
    text-align: center;
    position: relative;
}

.dropmenu .header-menu-img{
	max-width: 100%;
	height: 100px;
	background-size: cover;
	margin-bottom: 10px;
	object-fit: cover;
}
.dropmenu li> a:hover, 
.dropmenu li.current-page-parent >a, 
.dropmenu li.current-menu-item >a, 
.dropmenu li.current-menu-ancestor > a,
a.dropdown-item:hover {
    color: #00b680 !important;
}
.bx-wrapper .bx-pager.bx-default-pager a{
	width: 80px !important;
	background: #fff !important;
	height: 7px !important;
	padding: 0 !important;
	margin: 5px !important;
	border-radius: 0;
}
.bx-wrapper .bx-pager.bx-default-pager a.active{
	background: #0cc784 !important;
}
.bx-wrapper .bx-pager{
	text-align: left;
	width: auto;
}
.bx-wrapper .bx-pager,
.bx-wrapper .bx-controls-auto{
	bottom: 0 !important;
	left: 25px;
}
.bx-wrapper .bx-controls-direction a{
	width: 39px;
	height: 39px;
	display: none;
}
.bx-wrapper .bx-prev{
	background: url(../images/bx_left_arrow.png) no-repeat right center;
	background-size: 39px;
}
.bx-wrapper .bx-next{
	background: url(../images/bx_right_arrow.png) no-repeat right center;
	background-size: 39px;
}
.bx-wrapper .bx-prev:hover,
.bx-wrapper .bx-next:hover{
	background-position: right center;
}
/*end of header and slider*/

/*news_area*/
.news_area_bl{
	min-width:100%;
}
.news_area_bl .news_areain{
	max-width:1100px;
	margin:0 auto;
	padding:30px 0;
	font-family: Noto Sans CJK JP;
}
.news_area_bl .news_areain h3{
	color: #333333;
	font-size: 21px;
	font-weight: bold;
	padding-bottom: 20px;
}
.news_area_bl .news_areain .topics-inc .news .row2 p a{
	font-size: 16px;
}
.news_area_bl .news_areain .topics-inc{
	height:265px;
	padding:25px 25px 0 0;
	overflow:auto;
}
.news_area_bl .news_areain .topics-inc .news .row2 p a{
	font-size: 16px;
}

.news_area_bl .news_areain .topics-inc ul.news{
	border-bottom:1px dotted #444;
	overflow: hidden; /* MacIE */
	display: flex;
	align-items: center;
	gap: 16px;
}
ul.news:first-child{
	border-top: 1px dotted #444;
}
/* NN7 */
ul.news:after{
	content: "";
	display: block;
	clear: both;
	height: 1px;
	overflow: hidden;
}
/* IE6 */
* html ul.news{
	height: 1em;
	overflow: visible;
}
.row1{
	float:left;
	width: 16%;
	margin:10px 0;
	position:relative;
	padding-left:15px;
	font-size: 16px;
	font-family: Noto Sans CJK JP;
}
.category-name{
	float:left;
	width: 20%;
	margin:10px 0;
	position:relative;
	padding: 4px 0;
	font-size: 16px;
	text-align: center;
	background: #ffe1da;
	border-radius: 20px;
	font-family: Noto Sans CJK JP;
}
.category-name a{
	text-decoration: none;
	cursor: unset;
	color: #000;
	border-radius: 50%;
}
.row2{
	float:right;
	width: 64%;
	margin:10px 0;
	line-height: 1.5;
}
.row2 p{
	font-size: 16px;
	font-family: Noto Sans CJK JP;
	color: #333333;
}
.row2 p a{
	color: #00b680;
}
.row2 p a:hover{
	color: #808080;
}
div.topics-inc::-webkit-scrollbar-track
{
	background-color: #f2f2f2;
}

div.topics-inc::-webkit-scrollbar
{
	width: 10px;
	background-color: #f2f2f2;
}

div.topics-inc::-webkit-scrollbar-thumb
{
	background-color: #cccccc;
}
/*end of news_area*/

/*asmo_group*/
.asmo_group{
	background: #e6e6e6;
	width: 100%;
	text-align: center;
	padding: 20px 0;
	font-size: 30px;
	line-height: 1;
	position: relative;
}
.asmo_inner{
	max-width: 1020px;
    margin: 0 auto;
    width: 100%;
    padding: 0 40px;
}
.txt_asmo_01{
	color: #00b680;
	padding: 0 0 20px 0;
	font-size: 30px;
	font-weight: bold;
	font-family: 'FGPsegc3';
}
.asmo_group_ul{
	display: flex;
  	flex-wrap: wrap;
  }
.asmo_group_ul li{
	width: 32.233%;
	float: left;
	margin: 0 10px 10px 0;
}
.asmo_group_ul li img{
	width: 100%;
}
.asmo_content{
	display: inline-block;
	max-width: 338px;
	width: 100%;
	margin: 0 auto;
	 -webkit-transition: transform .5s ease-in-out;
    -moz-transition: transform .5s ease-in-out;
    transition: transform .5s ease-in-out;
}
.asmo_content:hover{
	-moz-transform: scale(1.2);
    -webkit-transform: scale(1.2);
    -o-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -webkit-transform: scale(1.2);
    transform: scale(1.2);
    -webkit-box-shadow: -1px -2px 30px 2px rgba(0,0,0,0.75);
	-moz-box-shadow: -1px -2px 30px 2px rgba(0,0,0,0.75);
	box-shadow: -1px -2px 30px 2px rgba(0,0,0,0.75);
}
.asmo_group_ul li:nth-child(3n+1) .asmo_content{
	float: left;
}
.asmo_group_ul li:nth-child(3n+3) .asmo_content{
	float: right;
}
/*end of asmo_group*/

/*contact_area*/
.contact_area{
	width: 100%;
	min-width: 100%;
	text-align: center;
}
.contact_area_inner{
	max-width: 1100px;
	margin: 0 auto;
	padding: 70px 0;
}
.contact_area .contact_area_inner .contact_tel.only_pc{
	width: 310px;
	height: 100px;
	display: inline-block;
	margin-right: 25px;
}
.contact_area .contact_area_inner .contact_inq{
	/* width: 230px; */
	height: 140px;
	display: inline-block;
	margin-left: 25px;
}
.contact_area .contact_area_inner .contact_tel img,
.contact_area .contact_area_inner .contact_inq img{
	width: 100%;
	height: 100%;
	vertical-align: unset;
}
/*end of contact_area*/

/*footer*/
.footer{
	min-width: 100%;
	background: #4d4d4d;
}
.footerin{
	max-width: 1600px;
	margin: 0 auto;
	padding: 25px 0;
	text-align: center;
}
.footerin ul{
	display: inline-block;
	width: 19%;
	vertical-align: top;
	text-align: left;
}
#menu-footer-menu1,
#menu-footer-menu2,
#menu-footer-menu3,
#menu-footer-menu4{
	padding: 0 0 0 60px;
}
.footerin ul li ul{
	width: 100%;
	padding: 13px 0;
	box-sizing: border-box;
}
.footerin ul li ul li ul{
	padding: 10px 0 0 0;
}
.footerin .heading01,
.heading01_02{
	font-size: 13px;
	line-height: 1;
	color: #adc743;
	padding: 0 0 15px 0;
	margin: 0 0 15px 0;
}
.heading01_02{
	padding: 15px 0 10px 0;
	margin: 0 0 10px 0;
}
.footerin .heading01 a,
.heading01_02 a{
	font-family: Noto Sans CJK JP;
	font-size: 13px;
	line-height: 1.5;
	color: #fff;
}
.footerin ul li.heading02,
.footerin ul li.heading03{
	position: relative;
	padding: 0 0 10px 13px;
}
.heading01 a,
.heading02 a,
.heading03 a{
	color: #fff;
	font-size: 13px;
	font-family: Noto Sans CJK JP;
	line-height: 1;
}
.heading01 a:hover,
.heading02 a:hover,
.heading03 a:hover{
	color: #00b680;
}
.footerin ul li.heading03{
	padding: 0 0 10px 13px;
}
.footerin ul li.heading03:last-child{
	padding-bottom: 0;
}
.footerin .footer_address{
	padding: 0 0 25px 20px;
}
.footer_address .fb_btn a,
.footer_address .insta_btn a{
	display: inline-block;
	float: left;
	margin-right: 20px;
}
.footer_address li img{
	width: 30px;
	height: 30px;
}
.fa-facebook-f{
	color: #ffffff;
	background: #1778f2;
	font-size: 20px;
	padding: 10px 15px;
	border-radius: 50%;
}
.insta_btn{
  display: inline-block;
  text-align: center;
  color: #2e6ca5;
  font-size: 20px;
  text-decoration: none;
}
.insta_btn:hover{
  color:#668ad8;
  transition: .5s;
}
.insta_btn .insta{
  position: relative;
  display: inline-block;
  width: 40px;
  height: 40px;
  background: -webkit-linear-gradient(135deg, #427eff 0%, #f13f79 70%) no-repeat;
  background: linear-gradient(135deg, #427eff 0%, #f13f79 70%) no-repeat;
  overflow: hidden;
  border-radius: 13px;
}
.insta_btn .insta:before{
  content: '';
  position: absolute;
  top: 23px;
  left: -18px;
  width: 40px;
  height: 40px;
  background: -webkit-radial-gradient(#ffdb2c 10%, rgba(255, 105, 34, 0.65) 55%, rgba(255, 88, 96, 0) 70%);
  background: radial-gradient(#ffdb2c 10%, rgba(255, 105, 34, 0.65) 55%, rgba(255, 88, 96, 0) 70%);
}
.insta_btn .fa-instagram{
  color: #FFF;
  position: relative;
  z-index: 2;
  font-size: 25px;
  line-height: 40px;
}
#onhover{
 	display: none;
}
#group_img_change:hover img[id="initial"]{
 	display: none;
}
#group_img_change:hover img[id="onhover"]{
 	display: block;
}
.footer_address_txt{
	font-size: 13px;
	font-family: Noto Sans CJK JP;
	line-height: 1.8;
	color: #fff;
	margin: 0 0 20px 0;
}
.footer_address_txt a{
	color: #fff;
}
.footer_copyright{
	width: 100%;
	max-width: 1020px;
	margin: 0 auto;
	border-top: 3px solid #787878;
}
.footer_copyright p{
	width: 460px;
	font-size: 12px;
	line-height: 1.2;
	color: #ffffff;
	font-family: Noto Sans CJK JP;
	text-align: center;
	margin: 15px auto 0 auto;
	padding: 15px;
	border: none;
}
.pagetop{
	position: fixed;
	z-index:100;
	bottom: -50px;
	right: 20px;
}
.pagetop a{
	display: block;
	width: 50px;
	height: 50px;
	background:#ccc;
}
.pagetop a:after{
	display: block;
	content: "";
	position: absolute;
	top: 50%;
	left: 40%;
	width: 6px;
	height: 6px;
	margin: -4px 0 0 0;
	border-top: solid 3px #fff;
	border-right: solid 3px #fff;
	-webkit-transform: rotate(-45deg);
			transform: rotate(-45deg);
}
.pagetop a:hover{
	background:#00b67f;
}
/*end of footer*/

.scrollNav{
	position: fixed;
	top: -42px;
	left: 0;
	min-width:100%;
	background:#64a820;
	opacity:0.9;
	box-shadow:0px 3px 5px 0px #777;
	-moz-box-shadow:0px 3px 5px 0px #777;
	-webkit-box-shadow:0px 3px 5px 0px #777;
	-o-box-shadow:0px 3px 5px 0px #777;
	-ms-box-shadow:0px 3px 5px 0px #777;
	z-index:200;
	display: none;
}
.scrollNav ul{
	max-width:1020px;
	text-align:center;
	margin:0 auto;
}
.scrollNav ul li{
	display:inline-block;
	padding:0 20px;
	font-size:14px;
	letter-spacing:1px;
	position: relative;
	width:auto !important;
	height:35px !important;
	line-height:35px !important;
	float:none !important;
	border-left:1px solid #a8d57c !important;
}
.scrollNav ul li:last-child{
	border-right:1px solid #a8d57c !important;
}
.scrollNav ul li a{
	text-decoration:none;
	display:block;
	padding:0 !important;
	color:#555353;
	font-weight:bold;
}
.scrollNav ul li a:hover{
	color:#ddd !important;
}
.scrollNav ul li ul li{
	width: 160% !important;
	border-left:0 !important;
	border-right:0 !important;

}
.scrollNav ul li ul li a{
	border-top: 1px solid #a8d57c;
	background: #7ec537;
	color:#fff;
	padding:0 5px !important;
	text-align:left !important;
}
.scrollNav ul li:hover > a{
	background: #64a820 !important;
	color:#fff;
}
.scrollNav ul li a:hover{
	background: #64a820 !important;
}
ul.bxslider li img.bxpc{
	display: block;
	height: 720px;
}
ul.bxslider li img.bxsp{
	display: none;
}
.wrapper{
	min-width:100%;
	border-top: 1px solid #e2e2e2;
}

/*facebook page*/
.fb_wrapper{
	padding: 80px 0;
}
.fb-box{
	margin: 0 auto;
	width: 1150px;
	padding: 0 25px;
}
.fb-page{
	padding: 0 25px 40px 20px;
}
/*end of facebook page*/

/*inquiry page*/
.inquiry_wrapper{
	padding: 80px 0;
}
.inquiry_inner{
	max-width: 1150px;
	margin: 0 auto;
}
.inquiry_title01 h3{
	padding: 0 0 15px 0;
	font-size: 21px;
	color: #00b67f;
	font-weight: bold;
}
.inquiry_inner .left_col{
	width: 45%;
	display: block;
	box-sizing: border-box;
	padding-right: 7px;
}
.inquiry_inner .right_col{
	width: 55%;
	display: block;
	box-sizing: border-box;
}
.inquiry_inner .right_col img{
	width: 100%;
}
.inquiry_inner .contact_from_tel{
	margin-bottom: 30px;
}
.inquiry_inner .inq_contact{
	line-height: 2;
	font-size: 17px;
	color: #555353;
}
.inquiry_inner .inq_contact span{
	color: #222222;
	font-weight: bold;
	font-size: 15px;
	display: block;
	line-height: 1.5;
}
.inquiry_inner .inq_contact .contact_phno{
	font-weight: bold;
	font-size: 22px;
}
.contact_from_web .btn_form{
	background: none;
	color: #ff8827;
	font-size: 15px;
	border: 4px solid #ffd6a9;
	padding: 15px;
	font-weight: bold;
	margin-bottom: 40px;
}
.contact_from_web .btn_form:hover,
.contact_from_web .btn_form:focus{
	background: #ff8827;
	color: #ffffff;
}
.contact_from_web .btn_form:focus{
	outline: none;
}
.contact_from_web p{
	font-size: 15px;
	color: #222222;
	line-height: 1.5;
}
.modal{
	display: block;
	margin: 100px auto 0 auto;
	width: 100%;
}

/***** table_Form *****/
.inquiry_content table{
	border-collapse: collapse !important;
    border-spacing: 0 !important;
}
.inquiry_content .table_Form{
	width: 100%;
	margin:0 auto 20px auto;
}
.inquiry_content .table_Form, 
.inquiry_content .table_Form th, 
.inquiry_content .table_Form td{
	border-bottom: 1px solid #fff; 
	border-collapse: collapse;
	font-size: 15px;
}
.inquiry_content .table_Form th{
	width:30%;
	padding: 15px;
	vertical-align:top;
	font-weight:bold;
	background: #ededed;
	text-align: left;
}
.inquiry_content .table_Form th div{
	position:relative;
	font-weight: bold;
}
.inquiry_content .table_Form th div img{
	position:absolute;
	top:4px;
	right:0;
}
.inquiry_content .table_Form td{
	padding: 15px;
	background:#f3f3f3;
}
.inquiry_content .table_Form td p,
.inquiry_content p{
	line-height: 1.5;
}
.inquiry_content .table_Form td input{
	width:85%;
	padding:5px;
	border: 1px solid #767676; 
	font-weight: normal;
	margin: 5px 0;
	font-size: 13px;
}
.inquiry_content .table_Form td input.wauto{
	width:auto;
	margin-right: 5px;
}
.inquiry_content .table_Form td input.w10{
	width:10%;
}
.inquiry_content .table_Form td textarea{
	width:85%;
	height: 150px;
	padding:5px;
	border: 1px solid #767676; 
	font-weight: normal;
}
.form_scroll{
	max-width:520px; 
	overflow:auto; 
	height:150px;
}
:focus::-webkit-input-placeholder{ 
	color: white; 
}
:focus:-moz-placeholder{ 
	color: white; 
} 
:focus::-moz-placeholder{ 
	color: white; 
}
:focus:placeholder-shown{ 
	color: white; 
}
.inquiry_content .inquiry_box{
	width:85%;
	font-size:12px;
	height:165px;
	padding:10px;
	overflow:auto;
}
.inquiry_content .inquiry_box li,
.inquiry_content .inquiry_box p{
	font-weight: normal;
	line-height: 1.5;
}
/***** btn_link *****/
.inquiry_content p.btn_link{
	width:230px;
	height:40px;
	margin:0 auto 20px auto;
}
.inquiry_content p.btn_link a{
	display:block;
	width:100%;
	height:100%;
	color:#fff;
	font-size:15px;
	font-weight:bold;
	text-align:center;
	vertical-align:middle;
	text-decoration:none;
	padding-top:13px;
	background:#ff8400;
	border:4px solid #ffd4a5;
	border-radius:5px;
}
.inquiry_content p.btn_link a:hover{
	background:#ff6c00;
	color:#fff;
}
.inquiry_content p.btn_link input{
	display:block;
	width:100%;
	height:60px;
	color:#fff;
	font-size:15px;
	font-weight:bold;
	text-align:center;
	vertical-align:middle;
	text-decoration:none;
	background:#ff8400;
	border:4px solid #ffd4a5;
	border-radius:5px;
}
.inquiry_content p.btn_link input:hover{
	background:#ff6c00;
}
.inquiry_content p.btn_link_back{
	width:230px;
	height:40px;
	margin:0 auto 30px auto;
}
.inquiry_content p.btn_link_back input{
	display:block;
	width:100%;
	height:60px;
	color:#fff;
	font-size:15px;
	font-weight:bold;
	text-align:center;
	vertical-align:middle;
	text-decoration:none;
	background:#aaa;
	border:4px solid #ccc;
	border-radius:5px;
}
.inquiry_content p.btn_link_back input:hover{
	background:#888;
}
.inquiry_content p.btn_link input:focus,
.inquiry_content p.btn_link_back input:focus{
	outline: none;
}
.inquiry_content .red{
    color: #a50918;
    margin: 5px 0 0 0;
}
/*end of inquiry page*/




/*privacy page*/
.privacy_inner{
	margin-bottom: 20px;
	padding: 80px 0;
}
.privacy_content h3{
	padding: 0 0 15px 0;
	font-size: 22px;
	color: #00b67f;
	font-weight: bold;
}
.privacy_content ol.ol_list{
    counter-reset: my-counter;
    padding: 0;
    margin-bottom: 30px;
    list-style: none;
}
.privacy_content ol.ol_list li{
    margin-bottom: 20px;
    padding-left: 30px;
    padding-right: 10px;
    position: relative;
    line-height: 1.5;
    color: #222222;
}
.privacy_content ol.ol_list li:before{
    content: counter(my-counter);
    counter-increment: my-counter;
    background-color: #00b67f;
    color: #fff;
    display: block;
    float: left;
    line-height: 22px;
    margin-left: -25px;
    text-align: center;
    height: 22px;
    width: 22px;
}
.privacy_content p.text_right{
    text-align: right;
    padding-right: 10px;
    line-height: 1.5;
}
.privacy_content .bold{
    font-weight: bold;
    font-size: 18px;
}
.privacy_content ol.ol_list li ul{
    counter-reset: list;
    margin-top: 15px;
    margin-left: 5px;
    list-style: none;
}
.privacy_content ol.ol_list li ul li:before{
    counter-increment: list;
    content: counter(list);
    font-weight: normal;
    border-radius: 50%;
}
.privacy_content .circle_green{
    background-color: #00b67f;
    color: #fff;
    padding: 0 6px;
    margin: 0 2px;
}
.privacy_content a{
	color: #00b67f;
	text-decoration: underline;
}
.privacy_content a:hover{
	opacity: .7;
}
/*end of privacy page*/

/*sitemap page*/
.sitemap_content{
	padding: 80px 0;
}
.sitemap_content h4{
    position: relative;
    font-size: 16px;
    font-weight: bold;
    padding: 0 0 0 5px;
    border-left: 6px solid #ccc;
    margin-bottom: 10px;
    color: #00b680;
}
.sitemap_content h4:before{
    position: absolute;
    left: -6px;
    bottom: 0;
    content: '';
    width: 6px;
    height: 50%;
    background-color: #00b680;
}
.sitemap_content h4:after{
    position: absolute;
    left: 0;
    bottom: 0;
    content: '';
    width: 100%;
    height: 0;
}
.sitemap_box{
    width: 45%;
    display: inline-block;
    vertical-align: top;
    margin: 0 auto;
    padding: 0 15px;
}
.sitemap_box ul{
	list-style: none;
}
.sitemap_box ul li.heading01{
    position: relative;
    padding-left: 20px;
    margin: 10px 0;
    font-weight: bold;
    line-height: 30px;
    border-bottom: dotted 2px #00b680;
    color: #00b680;
}
.sitemap_box ul li.heading02{
    position: relative;
    padding-left: 25px;
    margin-bottom: 5px;
}
.sitemap_box ul li.heading03{
    position: relative;
    padding-left: 30px;
    margin-bottom: 5px;
}
.sitemap_content h4 a,
.sitemap_box ul li.heading01 a,
.sitemap_box ul li.heading02 a,
.sitemap_box ul li.heading03 a{ 
    color: #00b680;
    text-decoration: underline;
}
.sitemap_content h4 a:hover,
.sitemap_box ul li.heading01 a:hover,
.sitemap_box ul li.heading02 a:hover,
.sitemap_box ul li.heading03 a:hover{
	opacity: .7;
}
.sitemap_box ul li.heading01:before{
    counter-increment: list;
    content: "";
    display: block;
    position: absolute;
    left: 0px;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background: #00b680;
    top: 50%;
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.sitemap_box ul li.heading01:after{
    content: "";
    display: block;
    position: absolute;
    left: 6px;
    height: 0;
    width: 0;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 5px solid #fff;
    top: 50%;
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.sitemap_box ul li.heading02:before{
    content: '';
    display: block;
    position: absolute;
    top: 5px;
    left: 13px;
    height: 0;
    width: 0;
    border-top: 6px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 9px solid #00b680;
}
.sitemap_box ul li.heading03:before{
    content: '';
    height: 1px;
    width: 10px;
    display: block;
    background: #00b680;
    position: absolute;
    top: 11px;
    left: 16px;
}
/*end of sitemap page*/

/* CSS for volunteer page */
.volunteer{
	padding: 80px 0;
}
.top_content02_volunteer{
    max-width: 1150px;
    margin: 0 auto;
    padding: 50px 0;
}
.volunteer .imgRight{
    float: right;
    margin: 0 0 30px 20px;
    line-height: 0;
}
.volunteer .imgLeft{
    float: left;
    margin: 0 20px 30px 0;
    line-height: 0;
}
.volunteer_area{
    min-width: 100%;
    min-height: 450px;
    background: url(../volunteer/images/bg_volunteer.gif);
    background-position: center center;
    background-size: cover;
}
.top_content02_volunteer,
.volunteer_areain{
    max-width: 1150px;
    margin: 0 auto;
    padding: 30px 0;
}
.top_content02_volunteer{
	padding-bottom: 0;
}
.volunteer_areain h4{
    position: relative;
    font-size: 16px;
    font-weight: bold;
    color: #00b67f;
    padding: 0;
    margin-bottom: 10px;
}
.volunteer_areain .image_box{
    text-align: center;
    margin-bottom: 30px;
}
.volunteer_areain .image_box li{
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    padding: 0 10px;
    margin-bottom: 20px;
}
.volunteer_areain .zoom{
    overflow: hidden;
}
.top_content02_volunteer .h3_common h3:after{
	left: 30%;
}
.top_content02_volunteer .cnt_01,.top_content02_volunteer .cnt_03{
	margin-bottom: 100px;
}
.top_content02_volunteer h3{
	font-size: 30px;
	font-weight: bold;
}
.top_content02_volunteer .cnt_01 .imgRight{
	width: 348px;
}
.top_content02_volunteer .cnt_01 .txt_content{
	width: 759px;
}
.top_content02_volunteer .cnt_01 p span{
	font-size: 18px;
	line-height: 1.5em;
}
.top_content02_volunteer .cnt_02 .imgLeft{
	width: 268px;
}
.top_content02_volunteer .cnt_02 .txt_content{
	width: 840px;
	float: left;
}
.top_content02_volunteer .cnt_02 p{
	font-size: 15px;
	line-height: 1.5em;
}
.volunteer_areain{
	padding: 50px 0 0;
}
.volunteer_areain h4{
	font-size: 22px;
	color: #00b67f;
	margin-bottom: 20px;
	font-weight: bold;
}
.volunteer_areain h4:before{
	content: none;
}
.volunteer_areain .image_box{
	width: 1150px;
	text-align: left;
	margin-bottom: 0;
}
.volunteer_areain .image_box li{
	padding: 0 27px;
	margin-bottom: 60px;
}
.volunteer_areain .image_box .first_img{
	padding-left: 0;
}
.volunteer_areain .image_box .last_img{
	padding-right: 0;
}
/* End CSS for volunteer page */

/*service-food page & regional-cuisine*/
.service_foodinner,
.regional_inner{
	padding: 80px 0;
}
.bg_eventfood{
    max-width: 960px;
    margin: 0 auto;
    padding: 30px 40px;
/*     background: url(../service/images/bg_eventfood.jpg); */
    border-radius: 20px;
}
.bg_eventfood a{
	text-align: left;
	color: #00b67f;
	text-decoration: underline;
}
.bg_eventfood a:hover{
	opacity: .7;
	text-decoration: none;
}
.arrow01{
    position: relative;
    padding-left: 20px;
    margin-bottom: 20px;
}
.arrow01:before{
    counter-increment: list;
    content: "";
    display: block;
    position: absolute;
    left: 0px;
    height: 15px;
    width: 15px;
    border-radius: 50%;
    background: #00b67f;
    top: 50%;
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.arrow01:after{
    content: "";
    display: block;
    position: absolute;
    left: 6px;
    height: 0;
    width: 0;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-left: 5px solid #fff;
    top: 50%;
    -moz-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}
.wp-block-image{
    max-width: 100%;
    margin-bottom: 1em;
    margin-left: 0;
    margin-right: 0;
    text-align: center;
}
.wp-block-image img{
    max-width: 100%;
    width: 60% !important;
}
.wp-block-image img.bigger{
	width: 100% !important;
	max-width: 100%;
}
dl.accordion{
    max-width: 100%;
    margin-bottom: 30px;
}
.table_accordion{
    min-width: 100%;
    padding: 0;
    background: #effce2;
}
dl.accordion dt{
    position: relative;
    height: 45px;
    line-height: 45px;
    padding-left: 30px;
    font-size: 110%;
    font-weight: bold;
    color: #fff;
    background: #66ad21;
    border-bottom: 1px solid #85c942;
}
dl.accordion dt:after{
    display: block;
    content: "";
    position: absolute;
    top: 45%;
    left: 10px;
    width: 6px;
    height: 6px;
    margin: -4px 0 0 0;
    border-top: solid 3px #fff;
    border-right: solid 3px #fff;
    -webkit-transform: rotate(135deg);
    transform: rotate(135deg);
}
.table_accordion th{
    width: 20%;
    font-weight: bold;
    vertical-align: top;
    border-bottom: 1px dotted #666666;
    padding: 10px;
}
.table_accordion td{
    width: 80%;
    border-bottom: 1px dotted #666666;
    padding: 10px;
}
/*end of service-food page & regional-cuisine*/

/*information*/
.tabs{
	padding: 85px 0 50px 0;
}
.tabs >ul{
	width: 460px;
	margin: 0 auto 100px auto;
	border: 1px solid #00b67f;
	border-radius: 20px;
}
.tabs >ul li{
	text-align: center;
	border-right: 1px solid #00b67f;
}
.tabs >ul li:last-child{
	border-right: none;
}
.tabs >ul li a{
	font-size: 18px;
	line-height: 1;
	color: #00b67f;
	padding: 12px 0;
	display: block;
}
.tabs >ul li.ui-tabs-active a{
	background: #00b67f;
	color: #fff;
}
.tabs >ul li a:focus{
	outline: none;
}
.tabs >ul li:first-child a{
	border-top-left-radius: 20px;
	border-bottom-left-radius: 20px;
}
.tabs >ul li:last-child a{
	border-top-right-radius: 20px;
	border-bottom-right-radius: 20px;
}
.information .content{
	width: 1150px;
	margin: 0 auto;
}
.information #information em{
	font-style: normal;
}
.information #information a[href^="http"]{
    background: url(../images/icon_blank.png) no-repeat 100% 50%;
    padding-right: 15px;
}
.information #information a:link,
.information #information a{
    color: #44800a;
    text-decoration: underline;
}
.information #information .table01{
	width: 1020px;
}
.information #greeting{
	width: 100%;
	margin: 0 auto;
}
.information #greeting ul li:first-child{
	padding: 0 15px 0 0;
	text-align: right;
}
.information #greeting ul li:last-child{
	padding: 0 0 0 15px;
}
.information #greeting p{
	font-size: 18px;
	line-height: 1.5;
	color: #222222;
	margin: 0 0 23px 0;
	text-align: left;
}
.information #greeting p:last-child{
	margin: 0;
}
.information #greeting .img_company{
	width: 550px;
	height: auto;
}
.information #greeting .img_sign{
	margin: 25px 0 0 0;
	width: 187px;
	height: auto;
}
/*end of information*/

/*business*/
.business .content{
    max-width: 1150px;
    margin: 0 auto 80px auto;
}
#bussiness_tabs.tabs >ul{
	width: 770px;
}
.business_ttl_01{
	font-size: 15px;
	line-height: 1;
	color: #222222;
	display: block;
	text-align: center;
	margin: 0 0 70px 0;
}
.business #business p,
#management_tabs #contest ul p,
#management_tabs #management ul p,
.service .service_ul li p{
	font-size: 18px;
	line-height: 1.5;
	color: #222222;
	padding: 0 0 30px 0;
}
.business .tabs_common ul,
#management_tabs #contest ul,
#management_tabs #management >ul{
	width: 1150px;
	margin: 0 auto 50px auto;
}
.business .padding_right,
#management_tabs #contest .padding_right,
#management_tabs #management .padding_right,
.service .service_ul .padding_right{
	padding: 0 33px 0 0;
}
.business .padding_left,
#management_tabs #management .padding_left{
	padding: 0 0 0 33px;
}
.business #business li img{
	width: 100%;
}
.business #business_system p{
	font-size: 18px;
	line-height: 1.5;
	color: #222222;
}
.imgRight{
    float: right;
    margin: 0 0 30px 45px;
    line-height: 0;
}
.business #process .content{
	width: 1140px;
	margin: 0 auto;
}
.business #process .tab-4-ttl{
	font-size: 18px;
	line-height: 1;
	color: #222222;
	padding: 0 0 25px 0;
}
.process-div{
	width: 500px;
	background: #e6e6e6;
	padding: 20px;
	margin: 0 0 80px 0;
}
.process-div-left{
	float: left;
}
.process-div-right{
	float: right;
}
.process-div h5{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
}
.process-div p{
	font-size: 15px;
	line-height: 1.5;
	color: #222222;
	padding: 20px 0 0 0;
	height: 105px;
}
.process-div img{
	width: 420px;
	height: auto;
	margin: 0 auto;
	display: block;
}
.graybox{
    padding: 30px;
    background: #e6e6e6;
    margin: 0 auto 30px auto;
}
.graybox h4{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	padding: 0 0 20px 0;
}
.graybox p{
	font-size: 15px;
	line-height: 1.5;
	color: #222222;
	border-bottom: 5px solid #fff;
	margin: 0 0 30px 0;
	padding: 0 0 30px 0;
}
.graybox p:last-child{
	margin: 0;
	border-bottom: none;
	padding: 0;
}
.graybox a{
	color: #00b67f;
	text-decoration: underline;
}
#trustsituation p{
	font-size: 22px;
	line-height: 1.5;
	color: #00b67f;
	font-weight: bold;
}
#trustsituation img{
    width: 877px;
    height: auto;
    margin: 80px auto 0 auto;
    display: block;
}
/*end of business*/

/*management*/
#management_tabs >ul{
	width: 930px;
	margin: 0 auto 100px auto;
}
#management_tabs #management .human_cap_item{
	width: 100%;
	/* background: #e6e6e6; */
	padding: 25px 0 70px 0;
}
#management_tabs .human_cap_item h4{
	text-align: center;
	font-size: 27px;
	line-height: 1;
	color: #00b680;
	font-weight: bold;
	padding: 0 0 20px 0;
}
#management_tabs .human_cap_item ul{
	width: 1180px;
	margin: 0 auto;
}
#management_tabs .human_cap_item ul li{
	text-align: center;
}
#management_tabs .human_cap_item ul li img{
	width: 260px;
	height: auto;
}
#management_tabs #management .list_of_major{
	width: 1150px;
	margin: 0 auto;
	padding: 40px 0;
}
#management_tabs #management .list_of_major h4{
	text-align: left;
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	padding: 0 0 15px 0;
}
#management_tabs #new_employee_training img{
	width: 1024px;
	height: auto;
	margin: 0 auto;
	display: block;
}
#management_tabs #employee_training{
	text-align: center;
}
#management_tabs #employee_training .img_employee_training_01{
	width: 30rem;
	height: auto;
	margin: 0 40px 0 0;
}
#management_tabs #employee_training .img_employee_training_02{
	width: 30rem;
	height: auto;
}
#management_tabs #contest .content{
	width: 1150px;
	margin: 0 auto;
}
#management_tabs #contest >ul li img{
	width: 100%;
}
#management_tabs #contest ul.ul_contest_img{
	width: 1180px;
	margin: 80px auto 0 auto;
}
#management_tabs #contest ul.ul_contest_img li{
	text-align: center;
}
#management_tabs #contest ul.ul_contest_img img{
	width: 360px;
	height: auto;
}
#management_tabs #cakes_course >p,
#management_tabs #employee_training >p,
#management_tabs #new_employee_training >p,
#management_tabs #management >p{
	font-size: 18px;
	line-height: 1.5;
	color: #222222;
	text-align: center;
	width: 1160px;
    margin: 0 auto 90px auto;
}
#management_tabs #cakes_course .div_bg{
	background: url(../images/bg_cake_course.jpg) top center no-repeat;
	background-size: cover;
	padding: 40px 0 0 0;
}
#management_tabs #cakes_course ul{
	width: 1170px;
	margin: 0 auto;
}
#management_tabs #cakes_course ul li{
	text-align: center;
	margin: 0 0 40px 0;
}
/*end of management*/

/*sitepolicy*/
.sitepolicy{
	width: 1150px;
	margin: 0 auto;
	padding: 100px 0;
}
.sitepolicy h3{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	padding: 0 0 15px 0;
}
.sitepolicy p{
	font-size: 15px;
	line-height: 1.5;
	color: #222222;
	padding: 0 0 25px 0;
}
.sitepolicy a{
	font-size: 15px;
	line-height: 1;
	color: #00b67f;
	text-decoration: underline;
}
/*end of sitepolicy*/

/*school*/
.school .elderly_bl_01 h5{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	padding: 0 0 10px 0;
}
.baby_food{
	padding: 100px 0;
	background: #e6e6e6;
	width: 100%;
}
.baby_food .inner{
	width: 1150px;
	margin: 0 auto;
}
.baby_food .table03{
	width: 1020px;
	margin: 0 auto;
	background: #fff;
}
.baby_food .table03 td:first-child{
	width: 445px;
}
.baby_food .table03 td:last-child{
	width: 575px;
}
.baby_food .image_ul{
	width: 975px;
	margin: 20px auto 50px auto;
}
.baby_food .image_ul img{
	width: 295px;
	margin: 0 auto;
	display: block;
}
.baby_food p{
	font-size: 15px;
	line-height: 1.5;
	color: #222222;
}
.food_education_ul{
	width: 100%;
}
.food_education_ul li:first-child{
	width: 730px;
	padding: 0 22px 0 0;
}
.food_education_ul li:last-child{
	width: 420px;
}
.food_education_ul img{
	width: 360px;
}
.food_education_ul img.img_school_06{
	width: 240px;
}
.school .schedulebox ul li{
	height: 365px;
}
/*end of school*/

/*start of office,elderly*/
.elderly_bl_01,
.office_bl_01,
.hospital_bl_01{	
	padding: 100px 0;
}
.elderly_bl_01 ul,
.office_bl_01 ul,
.hospital_bl_01 ul{
	margin: 0 auto;
	width: 1150px;
}
.elderly_bl_01 li:first-child,
.office_bl_01 li:first-child,
.hospital_bl_01 li:first-child{
	width: 510px;
	padding-right: 52px;
}
.elderly_bl_01 li:last-child,
.office_bl_01 li:last-child,
.hospital_bl_01 li:last-child{
	width: 640px;
}
.elderly_bl_01 li:last-child img,
.office_bl_01 li:last-child img,
.hospital_bl_01 li:last-child img{
	width: 640px;
	height: auto;
}
.elderly_bl_01 .first_content li p,
.office_bl_01 .first_content li p,
.hospital_bl_01 .first_content li p{
	color: #222222;
	margin-bottom: 29px;
	font-size: 18px;
	line-height: 1.5;
}
.elderly_bl_02,
.office_bl_02,
.hospital_bl_02{
	padding: 100px 0;
}
.elderly_bl_02 ul,
.office_bl_02 ul,
.hospital_bl_02 ul{
	padding-top: 20px;
	margin: 0 auto;
	width: 930px;
}
.elderly_bl_02 ul li:first-child,
.office_bl_02 ul li:first-child,
.hospital_bl_02 ul li:first-child{
	margin-right: 76px;
}
.elderly_bl_02 p,
.office_bl_02 p,
.hospital_bl_02 p{
	margin: 0 auto;
	width: 1150px;
	font-size: 18px;
	color: #222222;
	line-height: 1.5;
}
.elderly_bl_02 li,
.office_bl_02 li,
.hospital_bl_02 li{
	width: 426px;
}
.elderly_bl_02 li img,
.office_bl_02 li img,
.hospital_bl_02 li img{
	width: 426px;
	height: auto;
}
.elderly_bl_03,
.office_bl_03{
	padding: 100px 0;
}
.elderly_bl_03 h4,
.office_bl_03 h4,
.hospital_bl_04 h4{
	padding: 0 0 20px 0;
	margin: 0 auto;
	width: 1150px;
	font-size: 22px;
	color: #00b67f;
	line-height: 1;
	font-weight: bold;
}
.elderly_bl_03 li img{
	width: 265px;
	height: auto;
	padding-bottom: 16px;
}
.elderly_bl_03 li span{
	font-size: 18px;
	color: #222222;
	line-height: 1px;
}
.elderly_bl_03 .image_box{
	text-align: center;
	margin-bottom: 30px;
	width: 1180px;
	margin: 0 auto;
}
.elderly_bl_03 .image_box p{
	text-align: left;
	margin-left: 15px;
	font-size: 18px;
}
.office_bl_04,
.elderly_bl_04{
	padding: 100px 0;
}
.elderly_bl_04 .image_box li{
	width: 220px;
	display: inline-block;
}
.elderly_bl_04 .image_box{
	width: 660px;
	margin: 0 auto 30px;
}
.elderly_bl_04 .fourth_box{
	margin: 0 auto;
	width: 1150px;
}
.elderly_bl_04 .fourth_box p{
	font-size: 15px;
	color: #222222;
	line-height: 1.5;
}
.elderly_bl_04 .fourth_box h3{
	margin: 54px 0 16px 0;
	font-size: 22px;
	color: #00b67f;
	line-height: 1;
	text-align: center;
	font-weight: bold;
}
.elderly_bl_04 .fourth_box ul li img{
	width: 190px;
    height: auto;
    padding-bottom: 9px;
}
.elderly_bl_04 .fourth_box ul li span{
	font-size: 15px;
	color: #222222;
	line-height: 1;
	white-space: nowrap;
}
.elderly_bl_04 .image_box_02{
	padding-top: 30px;
}
.elderly_bl_04 .image_box_01 img{
	padding-bottom: 11px;
	width: 480px;
    height: auto;
}
.elderly_bl_04 .image_box_02 img{
	margin-bottom: 8px;
	width: 189px;
    height: auto;
}
.elderly_bl_05{
	padding: 100px 0;
}
.schedulebox,
.schedulebox01{
	width: 1150px;
	margin: 0 auto;
}
.schedulebox ul li{
	background: #e6e6e6;
	width: 265px;
	height: 435px;
}
.schedulebox01 ul li{
	background: #e6e6e6;
	width: 265px;
	height: 437px;
}
.schedulein p{
	padding: 20px 17px 8px 17px;
}
.schedulein p span:after{
	content:"\A"; white-space:pre; 
}
.elderly_bl_05 ul li em,
.hospital_bl_04 ul li em{
	font-style: normal;
	width: 265px;
	display: inline-block;
	padding: 15px 15px;
	font-size: 22px;
	font-weight: bold;
	color: #fffefe;
}
.elderly_bl_05 div h3 span{
	font-size: 16px;
	color: #222222;
	line-height: 1;
	font-weight: bold;
}
.schedule em{
	background-color: #fc9a9a;
}
.schedule_1 em{
	background-color: #4bd1e1;
}
.schedule_2 em{
	background-color: #f2974e;
}
.schedule_3 em{
	background-color: #8ca0e0;
}
.schedule_title{
	font-size: 18px;
	line-height: 1;
	color: #d95858;
	padding: 0 0 5px 0;
	display: block;
}
.schedule_desc{
	font-size: 15px;
	line-height: 1.5;
	color: #222222;
}
.schedule_title_01{
	color: #0493a4;
}
.schedule_title_02{
	color: #d66911;
}
.schedule_title_03{
	color: #44599a;
}
.elderly_bl_06{
	padding: 100px 0;
}
.elderly_bl_06 p{
	margin: 0 auto;
	width: 1150px;
	font-size: 18px;
	line-height: 1;
	color: #222222;
}
.elderly_bl_06 ul li img{
	width: 300px;
	height: auto;
}
.elderly_bl_06 ul{
	width: 680px;
	margin: 20px auto 48px auto;
}
.office_table,
.elderly_table{
	background-color: white;
	height: 575px;
	margin: 0 auto;
	width: 1020px;
	border-collapse: collapse;
    border-spacing: 0;
}
.office_table th,
.elderly_table th{
	font-size: 18px;
	color: #222222;
    padding: 15px;
    font-weight: bold;
    background: #f0f0f0;
    border-top: 2px dotted #ccc;
}
.office_table td,
.elderly_table td{
	color: #222222;
	font-size: 16px;
    padding: 15px;
    background: #fff;
    border-top: 2px dotted #ccc;
    border-bottom: 2px dotted #ccc;
    line-height: 1.3;
}
.elderly_bl_02,
.elderly_bl_04,
.elderly_bl_06,
.office_bl_02,
.office_bl_04,
.hospital_bl_02{
	background-color: #e6e6e6;
}
/*end of office,elderly*/

/*office*/
.office_bl_03 .title{
	text-align: center;
}
.office_bl_03 .image_box{
	text-align: center;
	margin-bottom: 30px;
	width: 660px;
	margin: 0 auto;
}
.office_bl_03 .image_box p{
	text-align: left;
	margin: 10px 0 30px 15px;
}
.office_bl_03 .description{
	width: 1150px;
    margin: 0 auto;
	font-size: 18px;
}
.office_bl_03 .grid_03 p{
	margin: 10px 0 30px 0;
	width: 100%;
	font-size: 18px;
	text-align: center;
}
.office_bl_03 .graybox{
	width: 1150px;
    padding: 20px;
    background: #f4f4f4;
    border: 1px solid #c9c9c9;
    margin: 0 auto 30px auto;
}
.layout_img_r{
    display: block;
    width: 1090px;
    margin: 0 auto;
}
.layout_img_r::after{
	margin-top: 25px;
	margin-bottom: 25px;
	content: " ";
	display: block;
	height: 5px;
	background: white;
}
.layout_img_r:last-child:after{
  	display: none;
}
.layout_img_r div.txt{
    display: inline-block;
    vertical-align: top;
    width: 742px;
    margin-bottom: 10px;
}
.layout_img_r div.txt p{
    font-size: 15px;
	line-height: 1.5;
	color: #222222;
	padding: 0 0 5px 0;
	margin: 0;
	border: none;
}
.office_bl_04 .layout_text,
.office_bl_04 .layout_text_01{
	width: 1150px;
    margin: 0 auto;
}
.office_bl_04 .layout_text h4{
	font-size: 22px;
	color: #00b67f;
	line-height: 1;
	padding-bottom: 20px;
}
.office_bl_04 .layout_text p{
	font-size: 15px;
	color: #222222;
	line-height: 1.5;
	padding-bottom: 46px;
}
.office_bl_04 .layout_text_01 p{
	font-size: 18px;
	color: #222222;
	line-height: 1.5;
	padding-bottom: 20px;
}
/*end of office*/

/*hospital*/
.service_hospital .tabs >ul{
	width: 460px;
}
.service_hospital .elderly_bl_01,
.service_hospital .elderly_bl_05{
	padding: 0 0 100px 0;
}
.meal_offer_eg{
	width: 400px;
	margin: 0 auto;
	padding: 100px 0 45px 0;
}
.meal_offer_eg img{
	width: 100%;
	margin: 0 0 10px 0;
}
.meal_offer_eg label{
	font-size: 18px;
	line-height: 1;
	color: #222222;
}
#hospital02 .elderly_bl_01{
	padding: 100px 0 45px 0;
}
#hospital02 .mealoffer_ul{
	width: 1170px;
	margin: 0 auto 30px auto;
}
.hospital02_mealoffer h4{
	width: 1150px;
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	margin: 0 auto 10px auto;
}
#hospital02 .meal_offer_div{
	width: 360px;
	margin: 0 auto;
}
#hospital02 .meal_offer_div img{
	width: 100%;
}
#hospital02 .meal_offer_div p{
	font-size: 18px;
	line-height: 1;
	color: #222222;
	margin: 10px 0 0 0;
}
#hospital02 .mealoffer_ul_01{
	width: 780px;
}
/*end of hospital*/

/*service*/
.service{
	width: 1150px;
	margin: 100px auto;
}
.service_ul{
	margin: 0 0 100px 0;
}
.service_three_major{
	margin: 60px 0 0 0;
}
.service_three_major p{
	font-size: 18px;
	line-height: 1.5;
	color: #222222;
}
.service_three_major label{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	display: block;
	margin: 45px 0 20px 0;
}
.service .img_service_06{
	width: 760px;
	height: auto;
	margin: 45px auto 0 auto;
	display: block;
}
/*end of service*/

/*annual_events*/
.annual_events{
	width: 1150px;
	margin: 100px auto;
}
.annual_events ul{
	width: 100%;
}
.events_div{
	width: 360px;
	margin: 0 auto 40px auto;
	text-align: center;
}
.events_div label{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	display: block;
	margin: 15px 0 10px 0;
}
.events_div span{
	font-size: 18px;
	line-height: 1;
	color: #222222;
}
/*end of annual_events*/

/*event*/
.event{
	padding: 100px 0;
}
.event_div_01{
	background: #defcf3;
	padding: 70px 0 0 0;
}
.event ul{
	width: 1180px;
	margin: 0 auto;
	padding: 0 0 50px 0;
}
.event ul img{
	width: 360px;
	height: auto;
	margin: 0 auto;
	display: block;
}
.event p{
	font-size: 22px;
	line-height: 1;
	color: #00b67f;
	font-weight: bold;
	padding: 0 0 10px 0;
	width: 1150px;
	margin: 0 auto;
}
.event_div_02{
	padding: 50px 0;
}
.event ul.event_div_02_ul{
	display: block;
	width: 1150px;
}
.event ul.event_div_02_ul li{
	width: auto;
	display: inline-block;
	margin: 0 30px 0 0;
}
.event_div_02_ul .img_width{
	width: 190px;
	height: auto;
}
/*end of event*/
.bnr_top{
	width: 100%;
}
.bnr_top img{
	width: 100%;
	height: auto;
	display: block;
	margin: 0 auto;
}

/*For TB*/
@media only screen and (min-width: 768px) and (max-width: 1366px){
	.h3_common{
		display: block;
		text-align: center;
		margin: 0 0 .70rem 0;
	}
	.h3_common h3{
		font-size: .30rem;
		line-height: 1;
		font-weight: bold;
		color: #222222;
		display: inline-block;
		position: relative;
	}
	.h3_common h3:after{
		content: " ";
		position: absolute;
		background: #00b67f;
		width: .50rem;
		height: .04rem;
		left: 50%;
		bottom: -.15rem;
		transform: translate(-50%);
	}
	/*table01*/
	.table01{
		width: 100%;
		margin: 0 auto .20rem auto;
	}
	.table01 th{
		width: 1.60rem;
		padding: .10rem;
		text-align: center;
		vertical-align: top;
		font-weight: bold;
		background: #f0f0f0;
		border-top: .02rem dotted #ccc;
		border-bottom: .02rem dotted #ccc;
		font-size: .18rem;
		line-height: 1.5;
	}
	.table01 td{
		padding: .10rem;
		background: #fff;
		border-top: .02rem dotted #ccc;
		border-bottom: .02rem dotted #ccc;
		line-height: 1.5;
		font-size: .18rem;
		line-height: 1.5;
		vertical-align: top;
	}
	.table01 td table td{
		padding: 0;
		border: none;
		line-height: 1.5;
	}
	.table01 .staff td{
		text-align: right;
		border: none;
		padding-right: .20rem;
	}
	/*end of table01*/

	/*table03*/
	.table03{
		width: 10.20rem;
		margin: 0 auto .20rem auto;
	}
	.table03 th{
		padding: .12rem;
		font-weight: bold;
		background: #eee;
		border: .01rem solid #ddd;
		text-align: left;
		font-size: .18rem;
		line-height: 1;
	}
	.table03 td{
		padding: .10rem;
		border: .01rem solid #ddd;
		width: 6.80rem;
		font-size: .18rem;
		line-height: 1;
	}
	.table03 td:last-child{
		width: 4.70rem;
	}
	/*end of table03*/
	#container{
		min-width:100%;
		display: block;
	}
	.inner{
	    max-width: 11.5rem;
	    width: 100%;
	    margin: 0 auto;
	    font-size: .13rem;
	}
	.title_area{
		min-width: 100%;
		background: #ededed;
	}
	.title_areain{
		width: 100%;
		/* max-width: 11.5rem; */
		margin: 0 auto;
		padding: 0;
		vertical-align: middle;
		box-sizing: border-box;
	}
	.title_area .title01{
		/* width: 40%; */
		width: 100%;
		float: left;
		padding: .82rem 0 .82rem 0;
	}
	.title_area .title01 h2{
		font-size: .28rem;
		font-weight: bold;
		/* border-left: .20rem solid #00b580; */
		padding: 0 .10rem;
	}
	.title_area .title01 h2 span{
		display: block;
		font-size: .17rem;
		font-weight: bold;
	}
	.title_area .common_logo{
		/* width: 3rem; */
		float: left;
		margin: 0 auto;
	}
	.title_area .common_logo img {
	    width: 100%;
	}
	/*header and slider*/
	.header{
		min-width:100%;
	}
	.headerin{
	    max-width: 12.2rem;
	    display: block;
	    margin: 0 auto .10rem auto;
	    border-bottom: .01rem solid #e2e2e2;
	}
	.headerin .logo{
	    float: left;
	    margin-top: .10rem;
	    margin-left: .20rem;
	    margin-right: .30rem;
	    margin-bottom: .10rem;
	}
	.headerin .logo img{
		width: 2.35rem;
		height: auto;
	}
	.top_right{
		display: inline-block;
		float: right;
		padding-top: .35rem;
		font-size: .13rem;
	}
	.top_right .sitemap{
	    border-right: .01rem solid #e2e2e2;
	    padding-right: .20rem;
	}
	.top_right .sitemap,
	.top_right .inquiry{
		display: inline-block;
		float: left;
		width: 1.1rem;
		height: auto;
		margin-right: .30rem;
	}
	.top_right img{
		width: 100%;
	}
	.top_right #onhover{
	  display: none;
	}
	.top_right #group_img_change:hover img[id="initial"]{
	  display: none;
	}
	.top_right #group_img_change:hover img[id="onhover"]{
	  display: block;
	}
	.nav{
	    min-width: 100%;
	    margin-bottom: .1rem;
	}
	.drawr{
	    max-height: .45rem !important;
	}
	.navin{
	    max-width: 12.2rem;
	    width: 97%;
	    margin: 0 auto;
	    display: block;
	}
	.dropmenu{
	    *zoom: 1;
	    padding-left: .10rem;
	    padding-right: .10rem;
	}
	.dropmenu:before, .dropmenu:after{
		content: "";
		display: table;
	}
	.dropmenu:after{
		clear: both;
	}
	.dropmenu li{
	    position: relative;
	    width: 20%;
	    line-height: 1.3;
	    float: left;
	    text-align: center;
	    border-right: .02rem solid #f0f0f0;
	    display: block;
	}
	.dropmenu li:hover{
	    background: #FAF8FA;
	}
	.dropmenu li:first-child{
	    border-left: .02rem solid #f0f0f0;
	}
	.dropmenu li:last-child{
		border-right: .02rem solid #f0f0f0;
	}
	#fade_in li ul{
	    opacity: 0;
	    top: 50%;
	    display: none;
	    width: 100%;
	}
	#fade_in li:hover ul {
    	top: 0;
    	display: block;
    	opacity: 1;
    	position: fixed;
    	z-index: 1111;
    	width: 100%;
    	height: 787px;
    	left: 50%;
    	right: 0;
    	margin-top: 145px;
    	transform: translateX(-50%);
	}
	.dropmenu li ul li {
		position: relative;
		line-height: 1.3;
		float: left;
		text-align: center;
		display: block;
		/* background: #ffe1da; */
		border-right: 1px solid #fff;
		border-bottom: 1px solid #fff;
		padding: 10px;
		width: 200px;
		position: relative;
	}
	.dropmenu li a{
	    display: block;
	    color: #4B4B4B;
	    font-size: .18rem;
	    font-weight: bold;
	    text-decoration: none;
	    padding: .13rem 0;
	}
	.dropmenu li a:link{
	    color: #4B4B4B;
	}
	.dropmenu li ul{
	    position: absolute;
	    z-index: 9999;
	    top: 100%;
	    left: 0;
	}
	.dropmenu li ul li{
	    /* width: 100%;
	    border-left: 0 !important;
	    border-right: 0 !important; */
	}
	.dropmenu li ul li a{
	    /* background: #FAF8FA; */
	    color: #4B4B4B;
	    padding: .05rem .15rem;
	    text-align: left;
	}
	.dropmenu li> a:hover, 
	.dropmenu li.current-page-parent >a, 
	.dropmenu li.current-menu-item >a, 
	.dropmenu li.current-menu-ancestor > a{
	    color: #00b680;
	}
	.bx-wrapper .bx-pager.bx-default-pager a{
		width: .80rem !important;
		background: #fff !important;
		height: .07rem !important;
		padding: 0 !important;
		margin: .05rem !important;
		border-radius: 0;
	}
	.bx-wrapper .bx-pager.bx-default-pager a.active{
		background: #0cc784 !important;
	}
	.bx-wrapper .bx-pager{
		text-align: left;
	}
	.bx-wrapper .bx-pager,
	.bx-wrapper .bx-controls-auto{
		bottom: 0 !important;
		left: .25rem;
	}
	.bx-wrapper .bx-pager{
		max-width: 100%;
		box-sizing: border-box;
	}
	.bx-wrapper .bx-controls-direction a{
		width: .39rem;
		height: .39rem;
		display: none;
	}
	.bx-wrapper .bx-prev{
		background: url(../images/bx_left_arrow.png) no-repeat right center;
		background-size: .39rem;
	}
	.bx-wrapper .bx-next{
		background: url(../images/bx_right_arrow.png) no-repeat right center;
		background-size: .39rem;
	}
	.bx-wrapper .bx-prev:hover,
	.bx-wrapper .bx-next:hover{
		background-position: right center;
	}
	/*end of header and slider*/

	/*news_area*/
	.news_area{
		min-width:100%;
	}
	.news_areain{
		max-width:10.2rem;
		margin:0 auto;
		padding:.3rem 0;
	}
	.news_areain h3{
		color: #333333;
		font-family: Noto Sans CJK JP;
		font-size: .22rem;
	}
	div.topics-inc{
		height:2.65rem;
		padding:.25rem .25rem 0 0;
		overflow:auto;
	}
	ul.news{
		border-bottom:.01rem dotted #444;
		overflow: hidden; /* MacIE */
	}
	ul.news:first-child{
		border-top: .01rem dotted #444;
	}
	/* NN7 */
	ul.news:after{
		content: "";
		display: block;
		clear: both;
		height: .01rem;
		overflow: hidden;
	}
	/* IE6 */
	* html ul.news{
		height: 1em;
		overflow: visible;
	}
	.row1{
		float:left;
		width:2rem;
		margin:.1rem 0;
		position:relative;
		padding-left:.15rem;
		font-size: .18rem;
		font-family: Noto Sans CJK JP;
	}
	.row2{
		float:right;
		width:7rem;
		margin:.1rem 0;
		line-height: 1.3;
		font-size: .18rem;
	}
	.row2 p{
		font-size: .18rem;
		font-family: Noto Sans CJK JP;
		color: #333333;
	}
	.row2 span{
		font-size: .18rem;
	}
	.row2 p a{
		color: #00b680;
	}
	.row2 p a:hover{
		color: #808080;
	}
	div.topics-inc::-webkit-scrollbar-track
	{
		background-color: #f2f2f2;
	}

	div.topics-inc::-webkit-scrollbar
	{
		width: .1rem;
		background-color: #f2f2f2;
	}

	div.topics-inc::-webkit-scrollbar-thumb
	{
		background-color: #cccccc;
	}
	/*end of news_area*/

	/*contact_area*/
	.contact_area{
		width: 100%;
		min-width: 100%;
		/* margin: .3rem auto; */
		background: #fff;
		text-align: center;
	}
	.contact_area_inner{
		max-width: 11.5rem;
		margin: 0 auto;
	}
	.contact_area .contact_area_inner .contact_tel.only_pc{
		width: 3.1rem;
		height: 1rem;
		display: inline-block;
		margin-right: .25rem;
	}
	.contact_area .contact_area_inner .contact_tel.only_sp{
		display: none;
	}
	.contact_area .contact_area_inner .contact_inq{
		/* width: 2.3rem; */
		height: 1rem;
		display: inline-block;
		margin-left: .25rem;
	}
	.contact_area .contact_area_inner .contact_tel img,
	.contact_area .contact_area_inner .contact_inq img{
		width: 100%;
		height: 100%;
		vertical-align: unset;
	}
	/*end of contact_area*/

	/*footer*/
	.footer{
		min-width: 100%;
		background: #4d4d4d;
	}
	.footerin{
		max-width: 13.67rem;
		margin: 0 auto;
		padding: .25rem 0;
		text-align: center;
	}
	.footerin ul{
		display: inline-block;
		width: 18%;
		vertical-align: top;
		text-align: left;
	}
	#menu-footer-menu1,
	#menu-footer-menu2,
	#menu-footer-menu3,
	#menu-footer-menu4{
		padding: 0 0 0 .5rem;
	}
	.footerin ul li ul{
		width: 100%;
		padding: .13rem 0;
		box-sizing: border-box;
	}
	.footerin ul li ul li ul{
		padding: .1rem 0 0 0;
	}
	.footerin .heading01,
	.heading01_02{
		font-size: .13rem;
		line-height: 1;
		color: #adc743;
		padding: 0 0 .15rem 0;
		margin: 0 0 .15rem 0;
	}
	.heading01_02{
		padding: .15rem 0 .1rem 0;
		margin: 0 0 .1rem 0;
	}
	.footerin .heading01 a,
	.heading01_02 a{
		font-family: Noto Sans CJK JP;
		font-size: .13rem;
		line-height: 1.5;
		color: #fff;
	}
	.footerin ul li.heading02,
	.footerin ul li.heading03{
		position: relative;
		padding: 0 0 .1rem .13rem;
	}
	.heading01 a,
	.heading02 a,
	.heading03 a{
		color: #fff;
		font-size: .13rem;
		font-family: Noto Sans CJK JP;
		line-height: 1;
	}
	.heading01 a:hover,
	.heading02 a:hover,
	.heading03 a:hover{
		color: #00b680;
	}
	.footerin ul li.heading03{
		padding: 0 0 .1rem .13rem;
	}
	.footerin ul li.heading03:last-child{
		padding-bottom: 0;
	}
	.footerin .footer_address{
		padding: 0 0 .25rem .2rem;
	}
	.footer_address .social_btn{
		font-size: .13rem;
	}
	.footer_address .fb_btn a,
	.footer_address .insta_btn a{
		display: inline-block;
		float: left;
		margin-right: .15rem;
	}
	.footer_address li img{
		width: .3rem;
		height: .3rem;
	}
	.fa-facebook-f{
		color: #ffffff;
	    background: #1778f2;
	    font-size: .2rem;
	    padding: .1rem .15rem;
	    border-radius: 50%;
	}
	.insta_btn{
	  display: inline-block;
	  text-align: center;
	  color: #2e6ca5;
	  font-size: .2rem;
	  text-decoration: none;
	}
	.insta_btn:hover{
	  color:#668ad8;
	  transition: .5s;
	}
	.insta_btn .insta{
	  position: relative;
	  display: inline-block;
	  width: .4rem;
	  height: .4rem;
	  background: -webkit-linear-gradient(135deg, #427eff 0%, #f13f79 70%) no-repeat;
	  background: linear-gradient(135deg, #427eff 0%, #f13f79 70%) no-repeat;
	  overflow: hidden;
	  border-radius: .13rem;
	}
	.insta_btn .insta:before{
	  content: '';
	  position: absolute;
	  top: .23rem;
	  left: -.18rem;
	  width: .4rem;
	  height: .4rem;
	  background: -webkit-radial-gradient(#ffdb2c 10%, rgba(255, 105, 34, 0.65) 55%, rgba(255, 88, 96, 0) 70%);
	  background: radial-gradient(#ffdb2c 10%, rgba(255, 105, 34, 0.65) 55%, rgba(255, 88, 96, 0) 70%);
	}
	.insta_btn .fa-instagram{
	  color: #FFF;
	  position: relative;
	  z-index: 2;
	  font-size: .25rem;
	  line-height: .4rem;
	}
	#onhover{
	 	display: none;
	}
	#group_img_change:hover img[id="initial"]{
	 	display: none;
	}
	#group_img_change:hover img[id="onhover"]{
	 	display: block;
	}
	.footer_address_txt{
		font-size: .13rem;
		font-family: Noto Sans CJK JP;
		line-height: 1.8;
		color: #fff;
		margin: 0 0 .2rem 0;
	}
	.footer_copyright{
		width: 100%;
		max-width: 10.2rem;
		margin: 0 auto;
		border-top: .03rem solid #787878;
	}
	.footer_copyright p{
		width: 4.6rem;
		font-size: .12rem;
		line-height: 1.2;
		color: #ffffff;
		font-family: Noto Sans CJK JP;
		text-align: center;
		margin: .15rem auto 0 auto;
		padding: .15rem;
		border: none;
	}
	.pagetop{
		position: fixed;
		z-index:100;
		bottom: -.5rem;
		right: .2rem;
	}
	.pagetop a{
		display: block;
		width: .5rem;
		height: .5rem;
		background:#ccc;
	}
	.pagetop a:after{
		display: block;
		content: "";
		position: absolute;
		top: 50%;
		left: 40%;
		width: .06rem;
		height: .06rem;
		margin: -.04rem 0 0 0;
		border-top: solid .03rem #fff;
		border-right: solid .03rem #fff;
		-webkit-transform: rotate(-45deg);
				transform: rotate(-45deg);
	}
	.pagetop a:hover{
		background:#00b67f;
	}
	/*end of footer*/
	.scrollNav{
		position: fixed;
		top: -.42rem;
		left: 0;
		min-width:100%;
		background:#64a820;
		opacity:0.9;
		box-shadow:0 .03rem .05rem 0 #777;
		-moz-box-shadow:0 .03rem .05rem 0 #777;
		-webkit-box-shadow:0 .03rem .05rem 0 #777;
		-o-box-shadow:0 .03rem .05rem 0 #777;
		-ms-box-shadow:0 .03rem .05rem 0 #777;
		z-index:200;
		display: none;
	}
	.scrollNav ul{
		max-width:10.2rem;
		text-align:center;
		margin:0 auto;
	}
	.scrollNav ul li{
		display:inline-block;
		padding:0 .2rem;
		font-size:.14rem;
		letter-spacing:.01rem;
		position: relative;
		width:auto !important;
		height:.35rem !important;
		line-height:.35rem !important;
		float:none !important;
		border-left:.01rem solid #a8d57c !important;
	}
	.scrollNav ul li:last-child{
		border-right:.01rem solid #a8d57c !important;
	}
	.scrollNav ul li a{
		text-decoration:none;
		display:block;
		padding:0 !important;
		color:#555353;
		font-weight:bold;
	}
	.scrollNav ul li a:hover{
		color:#ddd !important;
	}
	.scrollNav ul li ul li{
		width: 16rem !important;
		border-left:0 !important;
		border-right:0 !important;

	}
	.scrollNav ul li ul li a{
		border-top: .01rem solid #a8d57c;
		background: #7ec537;
		color:#fff;
		padding:0 .05rem !important;
		text-align:left !important;
	}
	.scrollNav ul li:hover > a{
		background: #64a820 !important;
		color:#fff;
	}
	.scrollNav ul li a:hover{
		background: #64a820 !important;
	}
	ul.bxslider li img.bxpc{
		display: block;
	}
	ul.bxslider li img.bxsp{
		display: none;
	}
	.wrapper{
		min-width:100%;
	}
	/*facebook page*/
	.fb_wrapper{
		padding: .8rem 0;
	}
	.fb-box{
		margin: 0 auto;
		width: 11.5rem;
		padding: 0 .25rem;
		display: inline-block;
	}
	.fb_wrapper .content{
		text-align: center;
	}
	.fb-page{
		width: 50%;
		float: left;
	    box-sizing: border-box;
	    padding: 0 .2rem;
	}
	.fb_iframe_widget span{
		width: 100% !important;
		margin: .2rem auto;
		box-sizing: border-box;
	}
	.fb_iframe_widget iframe{
	    width: 100% !important;
	}
	/*end of facebook page*/

	/*inquiry page*/
	.inquiry_wrapper{
		padding: .8rem 0;
	}
	.inquiry_inner{
		max-width: 11.5rem;
		margin: 0 auto;
	}
	.inquiry_title01 h3{
		padding: 0 0 .15rem 0;
		font-size: .25rem;
		color: #00b67f;
		font-weight: bold;
	}
	.inquiry_inner .left_col{
		width: 45%;
		display: block;
		box-sizing: border-box;
		padding-right: .07rem;
	}
	.inquiry_inner .right_col{
		width: 55%;
		display: block;
		box-sizing: border-box;
	}
	.inquiry_inner .right_col img{
		width: 100%;
	}
	.inquiry_inner .contact_from_tel{
		margin-bottom: .3rem;
	}
	.inquiry_inner .inq_contact{
		line-height: 2;
		font-size: .18rem;
		color: #555353;
	}
	.inquiry_inner .inq_contact span{
		color: #222222;
		font-weight: bold;
		font-size: .18rem;
		display: block;
		line-height: 1.5;
	}
	.inquiry_inner .inq_contact .contact_phno{
		font-weight: bold;
		font-size: .22rem;
	}
	.contact_from_web .btn_form{
		background: none;
		color: #ff8827;
		font-size: .18rem;
		border: .04rem solid #ffd6a9;
		padding: .15rem;
		font-weight: bold;
		margin-bottom: .4rem;
	}
	.contact_from_web .btn_form:hover,
	.contact_from_web .btn_form:focus{
		background: #ff8827;
		color: #ffffff;
	}
	.contact_from_web .btn_form:focus{
		outline: none;
	}
	.contact_from_web p{
		font-size: .18rem;
		color: #222222;
		line-height: 1.5;
	}
	.modal{
		display: block;
		margin: 1rem auto 0 auto;
		width: 100%;
	}
	/***** table_Form *****/
	.inquiry_content table{
		border-collapse: collapse !important;
	    border-spacing: 0 !important;
	}
	.inquiry_content .table_Form{
		width: 100%;
		margin:0 auto .35rem auto;
	}
	.inquiry_content .table_Form, 
	.inquiry_content .table_Form th, 
	.inquiry_content .table_Form td{
		border-bottom: .01rem solid #fff; 
		border-collapse: collapse;
		font-size: .18rem;
	}
	.inquiry_content .table_Form th{
		width:30%;
		padding: .15rem;
		vertical-align:top;
		font-weight:bold;
		background: #ededed;
		text-align: left;
	}
	.inquiry_content .table_Form th div{
		position:relative;
		font-weight: bold;
		font-size: .18rem;
	}
	.inquiry_content .table_Form th div img{
		position:absolute;
		top:.04rem;
		right:0;
	}
	.inquiry_content .table_Form td{
		padding: .15rem;
		background:#f3f3f3;
	}
	.inquiry_content .table_Form td p{
		font-size: .18rem;
		line-height: 1.3;
	}
	.inquiry_content .table_Form td label{
		font-size: .18rem;
	}
	.inquiry_content .table_Form td input{
		width:85%;
		padding:.05rem;
		border: .01rem solid #767676; 
		font-weight: normal;
		margin: .05rem 0;
		font-size: .18rem;
	}
	.inquiry_content .table_Form td input.wauto{
		width:auto;
		margin-right: .05rem;
		vertical-align: middle;
	}
	.inquiry_content .table_Form td input.w10{
		width:1rem;
	}
	.inquiry_content .table_Form td textarea{
		width:85%;
		height: 1.5rem;
		padding:.05rem;
		border: .01rem solid #767676; 
		font-weight: normal;
	}
	.form_scroll{max-width:5.2rem; overflow:auto; height:1.5rem;}
	:focus::-webkit-input-placeholder{ color: white; } /* Chrome・Safari・Opera用(※Edgeにも使える) */
	:focus:-moz-placeholder{ color: white; }  /* Firefox18以前用 */
	:focus::-moz-placeholder{ color: white; } /* Firefox19以上用 */
	:focus:placeholder-shown{ color: white; } /* CSS標準(予定)の記述 */

	.inquiry_content .inquiry_box{
		width:87%;
		font-size:.15rem;
		height:1.65rem;
		padding: .2rem .2rem .2rem .1rem;
		overflow:auto;
	}
	.inquiry_content .inquiry_box li,
	.inquiry_content .inquiry_box p{
		font-weight: normal;
		line-height: 1.5;
	}
	.inquiry_content .inquiry_box p{
		font-size: .15rem;
	}
	/***** btn_link *****/
	.inquiry_content p.btn_link{
		width:2.3rem;
		height:.4rem;
		margin:0 auto .2rem auto;
	}
	.inquiry_content p.btn_link a{
		display:block;
		width:100%;
		height:100%;
		color:#fff;
		font-size:.18rem;
		font-weight:bold;
		text-align:center;
		vertical-align:middle;
		text-decoration:none;
		padding-top:.13rem;
		background:#ff8400;
		border:.04rem solid #ffd4a5;
		border-radius:.05rem;
	}
	.inquiry_content p.btn_link a:hover{
		background:#ff6c00;
		color:#fff;
	}
	.inquiry_content p.btn_link input{
		display:block;
		width:100%;
		height:.6rem;
		color:#fff;
		font-size:.18rem;
		font-weight:bold;
		text-align:center;
		vertical-align:middle;
		text-decoration:none;
		background:#ff8400;
		border:.04rem solid #ffd4a5;
		border-radius:.05rem;
	}
	.inquiry_content p.btn_link input:hover{
		background:#ff6c00;
	}
	.inquiry_content p.btn_link_back{
		width:2.3rem;
		height:.4rem;
		margin:0 auto .3rem auto;
	}
	.inquiry_content p.btn_link_back input{
		display:block;
		width:100%;
		height:.6rem;
		color:#fff;
		font-size:.18rem;
		font-weight:bold;
		text-align:center;
		vertical-align:middle;
		text-decoration:none;
		background:#aaa;
		border:.04rem solid #ccc;
		border-radius:.05rem;
	}
	.inquiry_content p.btn_link_back input:hover{
		background:#888;
	}
	.inquiry_content p.btn_link input:focus,
	.inquiry_content p.btn_link_back input:focus{
		outline: none;
	}
	.inquiry_content .red{
	    color: #a50918;
	    margin: .05rem 0 0 0;
	}
	/*end of inquiry page*/

	/*privacy page*/
	.privacy_inner{
		margin-bottom: .2rem;
		padding: .8rem 0;
	}
	.privacy_content h3{
		padding: 0 0 .15rem 0;
		font-size: .30rem;
		color: #00b67f;
		font-weight: bold;
	}
	.privacy_content ol.ol_list{
	    counter-reset: my-counter;
	    padding: 0;
	    margin-bottom: .3rem;
	    list-style: none;
	}
	.privacy_content ol.ol_list li{
	    margin-bottom: .2rem;
	    padding-left: .3rem;
	    padding-right: .1rem;
	    position: relative;
	    line-height: 1.5;
	    color: #222222;
	    font-size: .18rem;
	}
	.privacy_content ol.ol_list li span{
		font-size: .18rem;
	}
	.privacy_content ol.ol_list li:before{
	    content: counter(my-counter);
	    counter-increment: my-counter;
	    background-color: #00b67f;
	    color: #fff;
	    display: block;
	    float: left;
	    line-height: .22rem;
	    margin-left: -.25rem;
	    text-align: center;
	    height: .22rem;
	    width: .22rem;
	}
	.privacy_content p.text_right{
	    text-align: right;
	    padding-right: .1rem;
	    line-height: 1.5;
	    font-size: .18rem;
	}
	.privacy_content .bold{
	    font-weight: bold;
	    font-size: .18rem;
	}
	.privacy_content ol.ol_list li ul{
	    counter-reset: list;
	    margin-top: .15rem;
	    margin-left: .05rem;
	    list-style: none;
	}
	.privacy_content ol.ol_list li ul li:before{
	    counter-increment: list;
	    content: counter(list);
	    font-weight: normal;
	    border-radius: 5rem;
	}
	.privacy_content .circle_green{
	    background-color: #00b67f;
	    color: #fff;
	    padding: 0 .06rem;
	    margin: 0 .02rem;
	}
	.privacy_content a{
		color: #00b67f;
		text-decoration: underline;
	}
	.privacy_content a:hover{
		opacity: .7;
	}
	/*end of privacy page*/

	/*sitemap page*/
	.sitemap_content{
		padding: .8rem 0;
	}
	.sitemap_content h4{
	    position: relative;
	    font-size: .16rem;
	    font-weight: bold;
	    padding: 0 0 0 .05rem;
	    border-left: .06rem solid #ccc;
	    margin-bottom: .1rem;
	    color: #00b680;
	}
	.sitemap_content h4:before{
	    position: absolute;
	    left: -.06rem;
	    bottom: 0;
	    content: '';
	    width: .06rem;
	    height: 50%;
	    background-color: #00b680;
	}
	.sitemap_content h4:after{
	    position: absolute;
	    left: 0;
	    bottom: 0;
	    content: '';
	    width: 100%;
	    height: 0;
	}
	.sitemap_box{
	    min-width: 4.5rem;
	    display: inline-block;
	    vertical-align: top;
	    margin: 0 auto;
	    padding: 0 .15rem;
	}
	.sitemap_box ul{
		list-style: none;
	}
	.sitemap_box ul li.heading01{
	    position: relative;
	    padding-left: .2rem;
	    margin: .1rem 0;
	    font-weight: bold;
	    line-height: .3rem;
	    border-bottom: dotted .02rem #00b680;
	    color: #00b680;
	    font-size: .13rem;
	}
	.sitemap_box ul li.heading02{
	    position: relative;
	    padding-left: .25rem;
	    margin-bottom: .05rem;
	    font-size: .13rem;
	}
	.sitemap_box ul li.heading03{
	    position: relative;
	    padding-left: .3rem;
	    margin-bottom: .05rem;
	    font-size: .13rem;
	}
	.sitemap_content h4 a,
	.sitemap_box ul li.heading01 a,
	.sitemap_box ul li.heading02 a,
	.sitemap_box ul li.heading03 a{ 
	    color: #00b680;
	    text-decoration: underline;
	    font-size: .18rem;
	}
	.sitemap_content h4 a{
		font-size: .22rem;
	}
	.sitemap_box ul li.heading01 a{
		font-size: .20rem;
	}
	.sitemap_content h4 a:hover,
	.sitemap_box ul li.heading01 a:hover,
	.sitemap_box ul li.heading02 a:hover,
	.sitemap_box ul li.heading03 a:hover{
		opacity: .7;
	}
	.sitemap_box ul li.heading01:before{
	    counter-increment: list;
	    content: "";
	    display: block;
	    position: absolute;
	    left: 0;
	    height: .15rem;
	    width: .15rem;
	    border-radius: 5rem;
	    background: #00b680;
	    top: 50%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.sitemap_box ul li.heading01:after{
	    content: "";
	    display: block;
	    position: absolute;
	    left: .06rem;
	    height: 0;
	    width: 0;
	    border-top: .04rem solid transparent;
	    border-bottom: .04rem solid transparent;
	    border-left: .05rem solid #fff;
	    top: 50%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.sitemap_box ul li.heading02:before{
	    content: '';
	    display: block;
	    position: absolute;
	    top: .05rem;
	    left: .13rem;
	    height: 0;
	    width: 0;
	    border-top: .06rem solid transparent;
	    border-right: .07rem solid transparent;
	    border-bottom: .06rem solid transparent;
	    border-left: .09rem solid #00b680;
	}
	.sitemap_box ul li.heading03:before{
	    content: '';
	    height: .01rem;
	    width: .1rem;
	    display: block;
	    background: #00b680;
	    position: absolute;
	    top: .11rem;
	    left: .16rem;
	}
	/*end of sitemap page*/

	/* CSS for volunteer page */
	.volunteer{
		padding: .8rem 0;
	}
	.top_content02_volunteer{
	    max-width: 11.5rem;
	    margin: 0 auto;
	    padding: .5rem 0;
	}
	.volunteer .imgRight{
	    float: right;
	    margin: 0 0 .3rem .2rem;
	    line-height: 0;
	}
	.volunteer .imgLeft{
	    float: left;
	    margin: 0 .2rem .3rem 0;
	    line-height: 0;
	}
	.volunteer_area{
	    min-width: 100%;
	    min-height: 4.5rem;
	    background: url(../volunteer/images/bg_volunteer.gif);
	    background-position: center center;
	    background-size: cover;
	}
	.top_content02_volunteer,
	.volunteer_areain{
	    max-width: 11.5rem;
	    margin: 0 auto;
	    padding: .3rem 0;
	}
	.top_content02_volunteer{
		padding-bottom: 0;
	}
	.volunteer_areain h4{
	    position: relative;
	    font-size: .16rem;
	    font-weight: bold;
	    color: #00b67f;
	    padding: 0;
	    margin-bottom: .1rem;
	}
	.volunteer_areain .image_box{
	    text-align: center;
	    margin-bottom: .3rem;
	}
	.volunteer_areain .image_box li{
	    display: inline-block;
	    vertical-align: middle;
	    text-align: left;
	    padding: 0 .1rem;
	    margin-bottom: .2rem;
	}
	.volunteer_areain .zoom{
	    overflow: hidden;
	}
	.top_content02_volunteer .h3_common h3:after{
		left: 30%;
	}
	.top_content02_volunteer .cnt_01,.top_content02_volunteer .cnt_03{
		margin-bottom: 1rem;
	}
	.top_content02_volunteer h3{
		font-size: .3rem;
		font-weight: bold;
	}
	.top_content02_volunteer .cnt_01 .imgRight{
		width: 3.48rem;
	}
	.top_content02_volunteer .cnt_01 .txt_content{
		width: 7.59rem;
	}
	.top_content02_volunteer .cnt_01 p span{
		font-size: .18rem;
		line-height: 1.5em;
	}
	.top_content02_volunteer .cnt_02 .imgLeft{
		width: 2.68rem;
	}
	.top_content02_volunteer .cnt_02 .imgLeft img,
	.top_content02_volunteer .cnt_01 .imgRight img{
		width: 100%;
	}
	.top_content02_volunteer .cnt_02 .txt_content{
		width: 8.4rem;
		float: left;
	}
	.top_content02_volunteer .cnt_02 p{
		font-size: .15rem;
		line-height: 1.5em;
	}
	.volunteer_areain{
		padding: .5rem 0 0;
	}
	.volunteer_areain h4{
		font-size: .22rem;
		color: #00b67f;
		margin-bottom: .2rem;
		font-weight: bold;
	}
	.volunteer_areain h4:before{
		content: none;
	}
	.volunteer_areain .image_box{
		width: 11.5rem;
		text-align: left;
		margin-bottom: 0;
	}
	.volunteer_areain .image_box li{
		width: 18%;
		padding: 0 .27rem;
		margin-bottom: .6rem;
	}
	.volunteer_areain .image_box .first_img{
		padding-left: 0;
	}
	.volunteer_areain .image_box .last_img{
		padding-right: 0;
	}
	.volunteer_areain .image_box .zoom img{
		width: 100%;
	}
	/* End CSS for volunteer page */

	/*service-food page & regional-cuisine*/
	.service_foodinner,
	.regional_inner{
		padding: .8rem 0;
		font-size: .13rem;
	}
	.bg_eventfood{
	    max-width: 9.6rem;
	    margin: 0 auto;
	    padding: .3rem .4rem;
	/*background: url(../service/images/bg_eventfood.jpg); */
	    border-radius: .2rem;
	    font-size: .13rem;
	}
	.bg_eventfood a{
		text-align: left;
		color: #00b67f;
		text-decoration: underline;
		font-size: .28rem;
	}
	.bg_eventfood a:hover{
		opacity: .7;
		text-decoration: none;
	}
	.arrow01{
	    position: relative;
	    padding-left: .3rem;
	}
	.arrow01:before{
	    counter-increment: list;
	    content: "";
	    display: block;
	    position: absolute;
	    left: 0px;
	    height: .25rem;
	    width: .25rem;
	    border-radius: 50%;
	    background: #00b67f;
	    top: 50%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.arrow01:after{
	    content: "";
	    display: block;
	    position: absolute;
	    left: .08rem;
	    height: 0;
	    width: 0;
	    border-top: .08rem solid transparent;
	    border-bottom: .08rem solid transparent;
	    border-left: .08rem solid #fff;
	    top: 50%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.wp-block-image{
	    max-width: 100%;
	    margin: .5rem 0;
	    text-align: center;
	}
	.wp-block-image img{
	    max-width: 100%;
	    width: 60% !important;
	}
	.wp-block-image img.bigger{
		width: 100% !important;
		max-width: 100%;
	}
	dl.accordion{
	    max-width: 100%;
	    margin: .5rem 0 .3rem 0; 
	}
	.table_accordion{
	    min-width: 100%;
	    padding: 0;
	    background: #effce2;
	}
	dl.accordion dt{
	    position: relative;
	    height: .45rem;
	    line-height: .45rem;
	    padding-left: .3rem;
	    font-size: .255rem;
	    font-weight: bold;
	    color: #fff;
	    background: #66ad21;
	    border-bottom: .01rem solid #85c942;
	}
	dl.accordion dt:after{
	    display: block;
	    content: "";
	    position: absolute;
	    top: 35%;
	    left: .1rem;
	    width: .08rem;
	    height: .08rem;
	    margin: -.04rem 0 0 0;
	    border-top: solid .03rem #fff;
	    border-right: solid .03rem #fff;
	    -webkit-transform: rotate(135deg);
	    transform: rotate(135deg);
	}
	.table_accordion th{
	    width: 25%;
	    font-weight: bold;
	    vertical-align: top;
	    border-bottom: .01rem dotted #666666;
	    padding: .1rem;
	    font-size: .255rem;
	}
	.table_accordion td{
	    width: 80%;
	    border-bottom: .01rem dotted #666666;
	    padding: .1rem;
	}
	/*end of service-food page & regional-cuisine*/

	/*information*/
	.tabs{
		padding: .85rem 0 .50rem 0;
	}
	.tabs >ul{
		width: 4.60rem;
		margin: 0 auto 1.00rem auto;
		border: .01rem solid #00b67f;
		border-radius: .20rem;
	}
	.tabs >ul li{
		text-align: center;
		border-right: .01rem solid #00b67f;
	}
	.tabs >ul li:last-child{
		border-right: none;
	}
	.tabs >ul li a{
		font-size: .18rem;
		line-height: 1;
		color: #00b67f;
		padding: .12rem 0;
		display: block;
	}
	.tabs >ul li.ui-tabs-active a{
		background: #00b67f;
		color: #fff;
	}
	.tabs >ul li a:focus{
		outline: none;
	}
	.tabs >ul li:first-child a{
		border-top-left-radius: .20rem;
		border-bottom-left-radius: .20rem;
	}
	.tabs >ul li:last-child a{
		border-top-right-radius: .20rem;
		border-bottom-right-radius: .20rem;
	}
	.information .content{
		width: 11.50rem;
		margin: 0 auto;
	}
	.information #information em{
		font-style: normal;
		font-size: .18rem;
	}
	.information #information a[href^="http"]{
	    background: url(../images/icon_blank.png) no-repeat 100% 50%;
	    padding-right: .15rem;
	    font-size: .18rem;
	}
	.information #information a:link,
	.information #information a{
	    color: #44800a;
	    text-decoration: underline;
	}
	.information #information .table01{
		width: 10.20rem;
	}
	.information #greeting{
		width: 100%;
		margin: 0 auto;
	}
	.information #greeting ul li:first-child{
		padding: 0 .15rem 0 0;
		text-align: right;
	}
	.information #greeting ul li:last-child{
		padding: 0 0 0 .15rem;
	}
	.information #greeting p{
		font-size: .18rem;
		line-height: 1.5;
		color: #222222;
		margin: 0 0 .23rem 0;
		text-align: left;
	}
	.information #greeting p:last-child{
		margin: 0;
	}
	.information #greeting .img_company{
		width: 5.50rem;
		height: auto;
	}
	.information #greeting .img_sign{
		margin: .25rem 0 0 0;
		width: 1.87rem;
		height: auto;
	}
	/*end of information*/

	/*business*/
	.business .content{
	    max-width: 11.50rem;
	    margin: 0 auto .80rem auto;
	}
	#bussiness_tabs.tabs >ul{
		width: 7.70rem;
	}
	.business_ttl_01{
		font-size: .15rem;
		line-height: 1;
		color: #222222;
		display: block;
		text-align: center;
		margin: 0 0 .70rem 0;
	}
	.business #business p,
	#management_tabs #contest ul p,
	#management_tabs #management ul p,
	.service .service_ul li p{
		font-size: .18rem;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 .30rem 0;
	}
	.business .tabs_common ul,
	#management_tabs #contest ul,
	#management_tabs #management >ul{
		width: 11.50rem;
		margin: 0 auto .50rem auto;
	}
	.business .padding_right,
	#management_tabs #contest .padding_right,
	#management_tabs #management .padding_right,
	.service .service_ul .padding_right{
		padding: 0 .33rem 0 0;
	}
	.business .padding_left,
	#management_tabs #management .padding_left{
		padding: 0 0 0 .33rem;
	}
	.business #business li img{
		width: 100%;
	}
	.business #business_system p{
		font-size: .18rem;
		line-height: 1.5;
		color: #222222;
	}
	.business #business_system p span{
		font-size: .18rem;
	}
	.imgRight{
	    float: right;
	    margin: 0 0 .30rem .45rem;
	    line-height: 0;
	}
	.business #process .content{
		width: 11.40rem;
		margin: 0 auto;
	}
	.business #process .tab-4-ttl{
		font-size: .18rem;
		line-height: 1;
		color: #222222;
		padding: 0 0 .25rem 0;
	}
	.process-div{
		width: 5.00rem;
		background: #e6e6e6;
		padding: .20rem;
		margin: 0 0 .80rem 0;
	}
	.process-div-left{
		float: left;
	}
	.process-div-right{
		float: right;
	}
	.process-div h5{
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
	}
	.process-div p{
		font-size: .15rem;
		line-height: 1.5;
		color: #222222;
		padding: .20rem 0 0 0;
		height: 1.05rem;
	}
	.process-div img{
		width: 4.20rem;
		height: auto;
		margin: 0 auto;
		display: block;
	}
	.graybox{
	    padding: .30rem;
	    background: #e6e6e6;
	    margin: 0 auto .30rem auto;
	}
	.graybox h4{
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .20rem 0;
	}
	.graybox p{
		font-size: .15rem;
		line-height: 1.5;
		color: #222222;
		border-bottom: .05rem solid #fff;
		margin: 0 0 .30rem 0;
		padding: 0 0 .30rem 0;
	}
	.graybox p:last-child{
		margin: 0;
		border-bottom: none;
		padding: 0;
	}
	.graybox a{
		color: #00b67f;
		text-decoration: underline;
	}
	#trustsituation p{
		font-size: .24rem;
		line-height: 1.5;
		color: #00b67f;
		font-weight: bold;
	}
	#trustsituation img{
	    width: 8.77rem;
	    height: auto;
	    margin: .80rem auto 0 auto;
	    display: block;
	}
	/*end of business*/

	/*management*/
	#management_tabs >ul{
		width: 9.30rem;
		margin: 0 auto 1.00rem auto;
	}
	#management_tabs ul li img{
		width: 100%;
	}
	#management_tabs #management .human_cap_item{
		width: 100%;
		background: #e6e6e6;
		padding: .25rem 0 .70rem 0;
	}
	#management_tabs .human_cap_item h4{
		text-align: center;
		font-size: .27rem;
		line-height: 1;
		color: #00b680;
		font-weight: bold;
		padding: 0 0 .20rem 0;
	}
	#management_tabs .human_cap_item ul{
		width: 11.80rem;
		margin: 0 auto;
	}
	#management_tabs .human_cap_item ul li{
		text-align: center;
	}
	#management_tabs .human_cap_item ul li img{
		width: 2.60rem;
		height: auto;
	}
	#management_tabs #management .list_of_major{
		width: 11.50rem;
		margin: 0 auto;
		padding: .40rem 0;
	}
	#management_tabs #management .list_of_major h4{
		text-align: left;
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .15rem 0;
	}
	#management_tabs #new_employee_training img{
		width: 10.24rem;
		height: auto;
		margin: 0 auto;
		display: block;
	}
	#management_tabs #employee_training{
		text-align: center;
	}
	#management_tabs #employee_training .img_employee_training_01{
		width: 2.96rem;
		height: auto;
		margin: 0 .40rem 0 0;
	}
	#management_tabs #employee_training .img_employee_training_02{
		width: 2.96rem;
		height: auto;
	}
	#management_tabs #contest .content{
		width: 11.50rem;
		margin: 0 auto;
	}
	#management_tabs #contest >ul li img{
		width: 100%;
	}
	#management_tabs #contest ul.ul_contest_img{
		width: 11.80rem;
		margin: .80rem auto 0 auto;
	}
	#management_tabs #contest ul.ul_contest_img li{
		text-align: center;
	}
	#management_tabs #contest ul.ul_contest_img img{
		width: 3.60rem;
		height: auto;
	}
	#management_tabs #cakes_course >p,
	#management_tabs #employee_training >p,
	#management_tabs #new_employee_training >p,
	#management_tabs #management >p{
		font-size: .18rem;
		line-height: 1.5;
		color: #222222;
		text-align: center;
		width: 11.60rem;
	    margin: 0 auto .90rem auto;
	}
	#management_tabs #cakes_course .div_bg{
		background: url(../images/bg_cake_course.jpg) top center no-repeat;
		background-size: cover;
		padding: .40rem 0 0 0;
	}
	#management_tabs #cakes_course ul{
		width: 11.70rem;
		margin: 0 auto;
	}
	#management_tabs #cakes_course ul li{
		text-align: center;
		margin: 0 0 .40rem 0;
	}
	#management_tabs #cakes_course ul li img{
		width: 3.3rem;
		height: auto;
	}
	/*end of management*/

	/*sitepolicy*/
	.sitepolicy{
		width: 11.50rem;
		margin: 0 auto;
		padding: 1.00rem 0;
	}
	.sitepolicy h3{
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .15rem 0;
	}
	.sitepolicy p{
		font-size: .18rem;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 .25rem 0;
	}
	.sitepolicy a{
		font-size: .18rem;
		line-height: 1;
		color: #00b67f;
		text-decoration: underline;
	}
	/*end of sitepolicy*/

	/*school*/
	.school .elderly_bl_01 h5{
		font-size: .22rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .10rem 0;
	}
	.baby_food{
		padding: 1.00rem 0;
		background: #e6e6e6;
		width: 100%;
	}
	.baby_food .inner{
		width: 11.50rem;
		margin: 0 auto;
	}
	.baby_food .table03{
		width: 10.20rem;
		margin: 0 auto;
		background: #fff;
	}
	.baby_food .table03 td:first-child{
		width: 4.45rem;
	}
	.baby_food .table03 td:last-child{
		width: 5.75rem;
	}
	.baby_food .image_ul{
		width: 9.75rem;
		margin: .20rem auto .50rem auto;
	}
	.baby_food .image_ul img{
		width: 2.95rem;
		margin: 0 auto;
		display: block;
	}
	.baby_food p{
		font-size: .15rem;
		line-height: 1.5;
		color: #222222;
	}
	.food_education_ul{
		width: 100%;
	}
	.food_education_ul li:first-child{
		width: 7.30rem;
		padding: 0 .22rem 0 0;
	}
	.food_education_ul li:last-child{
		width: 4.20rem;
	}
	.food_education_ul img{
		width: 3.60rem;
	}
	.food_education_ul img.img_school_06{
		width: 2.40rem;
	}
	.school .schedulebox ul li{
		height: 3.65rem;
	}
	/*end of school*/


	/*start of office,elderly*/
	.elderly_bl_01,
	.office_bl_01,
	.hospital_bl_01{	
		padding: 1.00rem 0;
	}
	.elderly_bl_01 ul,
	.office_bl_01 ul,
	.hospital_bl_01 ul{
		margin: 0 auto;
		width: 11.50rem;
	}
	.elderly_bl_01 li:first-child,
	.office_bl_01 li:first-child,
	.hospital_bl_01 li:first-child{
		width: 5.10rem;
		padding-right: .52rem;
	}
	.elderly_bl_01 li:last-child,
	.office_bl_01 li:last-child,
	.hospital_bl_01 li:last-child{
		width: 6.40rem;
	}
	.elderly_bl_01 li:last-child img,
	.office_bl_01 li:last-child img,
	.hospital_bl_01 li:last-child img{
		width: 6.40rem;
		height: auto;
	}
	.elderly_bl_01 .first_content li p,
	.office_bl_01 .first_content li p,
	.hospital_bl_01 .first_content li p{
		color: #222222;
		margin-bottom: .29rem;
		font-size: .18rem;
		line-height: 1.5;
	}
	.elderly_bl_02,
	.office_bl_02,
	.hospital_bl_02{
		padding: 1.00rem 0;
	}
	.elderly_bl_02 ul,
	.office_bl_02 ul,
	.hospital_bl_02 ul{
		padding-top: .20rem;
		margin: 0 auto;
		width: 9.30rem;
	}
	.elderly_bl_02 ul li:first-child,
	.office_bl_02 ul li:first-child,
	.hospital_bl_02 ul li:first-child{
		margin-right: .76rem;
	}
	.elderly_bl_02 p,
	.office_bl_02 p,
	.hospital_bl_02 p{
		margin: 0 auto;
		width: 11.50rem;
		font-size: .18rem;
		color: #222222;
		line-height: 1.5;
	}
	.elderly_bl_02 li,
	.office_bl_02 li,
	.hospital_bl_02 li{
		width: 4.26rem;
	}
	.elderly_bl_02 li img,
	.office_bl_02 li img,
	.hospital_bl_02 li img{
		width: 4.26rem;
		height: auto;
	}
	.elderly_bl_03,
	.office_bl_03{
		padding: 1.00rem 0;
	}
	.elderly_bl_03 h4,
	.office_bl_03 h4,
	.hospital_bl_04 h4{
		padding: 0 0 .20rem 0;
		margin: 0 auto;
		width: 11.50rem;
		font-size: .22rem;
		color: #00b67f;
		line-height: 1;
		font-weight: bold;
	}
	.elderly_bl_03 li img{
		width: 2.65rem;
		height: auto;
		padding-bottom: .16rem;
	}
	.elderly_bl_03 li span{
		font-size: .18rem;
		color: #222222;
		line-height: .01rem;
	}
	.elderly_bl_03 .image_box{
		text-align: center;
		margin-bottom: .30rem;
		width: 11.80rem;
		margin: 0 auto;
	}
	.elderly_bl_03 .image_box p{
		text-align: left;
		margin-left: .15rem;
		font-size: .18rem;
	}
	.office_bl_04,
	.elderly_bl_04{
		padding: 1.00rem 0;
	}
	.elderly_bl_04 .image_box li{
		width: 2.20rem;
		display: inline-block;
	}
	.elderly_bl_04 .image_box{
		width: 6.60rem;
		margin: 0 auto .30rem;
	}
	.elderly_bl_04 .fourth_box{
		margin: 0 auto;
		width: 11.50rem;
	}
	.elderly_bl_04 .fourth_box p{
		font-size: .15rem;
		color: #222222;
		line-height: 1.5;
	}
	.elderly_bl_04 .fourth_box h3{
		margin: .54rem 0 .16rem 0;
		font-size: .24rem;
		color: #00b67f;
		line-height: 1;
		text-align: center;
		font-weight: bold;
	}
	.elderly_bl_04 .fourth_box ul li img{
		width: 1.90rem;
	    height: auto;
	    padding-bottom: .09rem;
	}
	.elderly_bl_04 .fourth_box ul li span{
		font-size: .15rem;
		color: #222222;
		line-height: 1;
		white-space: nowrap;
	}
	.elderly_bl_04 .image_box_02{
		padding-top: .30rem;
	}
	.elderly_bl_04 .image_box_01 img{
		padding-bottom: .11rem;
		width: 4.80rem;
	    height: auto;
	}
	.elderly_bl_04 .image_box_02 img{
		margin-bottom: .08rem;
		width: 1.89rem;
	    height: auto;
	}
	.elderly_bl_05{
		padding: 1.00rem 0;
	}
	.schedulebox,
	.schedulebox01{
		width: 11.50rem;
		margin: 0 auto;
	}
	.schedulebox ul li{
		background: #e6e6e6;
		width: 2.65rem;
		height: 4.35rem;
	}
	.schedulebox01 ul li{
		background: #e6e6e6;
		width: 2.65rem;
		height: 4.37rem;
	}
	.schedulein p{
		padding: .20rem .17rem .08rem .17rem;
	}
	.schedulein p span:after{
		content:"\A"; white-space:pre; 
	}
	.elderly_bl_05 ul li em,
	.hospital_bl_04 ul li em{
		font-style: normal;
		width: 2.65rem;
		display: inline-block;
		padding: .15rem .15rem;
		font-size: .22rem;
		font-weight: bold;
		color: #fffefe;
	}
	.elderly_bl_05 div h3 span{
		font-size: .16rem;
		color: #222222;
		line-height: 1;
		font-weight: bold;
	}
	.schedule em{
		background-color: #fc9a9a;
	}
	.schedule_1 em{
		background-color: #4bd1e1;
	}
	.schedule_2 em{
		background-color: #f2974e;
	}
	.schedule_3 em{
		background-color: #8ca0e0;
	}
	.schedule_title{
		font-size: .18rem;
		line-height: 1;
		color: #d95858;
		padding: 0 0 .05rem 0;
		display: block;
	}
	.schedule_desc{
		font-size: .15rem;
		line-height: 1.5;
		color: #222222;
	}
	.schedule_title_01{
		color: #0493a4;
	}
	.schedule_title_02{
		color: #d66911;
	}
	.schedule_title_03{
		color: #44599a;
	}
	.elderly_bl_06{
		padding: 1.00rem 0;
	}
	.elderly_bl_06 p{
		margin: 0 auto;
		width: 11.50rem;
		font-size: .18rem;
		line-height: 1;
		color: #222222;
	}
	.elderly_bl_06 ul li img{
		width: 3.00rem;
		height: auto;
	}
	.elderly_bl_06 ul{
		width: 6.80rem;
		margin: .20rem auto .48rem auto;
	}
	.office_table,
	.elderly_table{
		background-color: white;
		height: 5.75rem;
		margin: 0 auto;
		width: 10.20rem;
		border-collapse: collapse;
	    border-spacing: 0;
	}
	.office_table th,
	.elderly_table th{
		font-size: .18rem;
		color: #222222;
	    padding: .15rem;
	    font-weight: bold;
	    background: #f0f0f0;
	    border-top: .02rem dotted #ccc;
	}
	.office_table td,
	.elderly_table td{
		color: #222222;
		font-size: .16rem;
	    padding: .15rem;
	    background: #fff;
	    border-top: .02rem dotted #ccc;
	    border-bottom: .02rem dotted #ccc;
	    line-height: 1.3;
	}
	.elderly_bl_02,
	.elderly_bl_04,
	.elderly_bl_06,
	.office_bl_02,
	.office_bl_04,
	.hospital_bl_02{
		background-color: #e6e6e6;
	}
	/*end of office,elderly*/

	/*office*/
	.office_bl_03 .title{
		text-align: center;
	}
	.office_bl_03 .image_box{
		text-align: center;
		margin-bottom: .30rem;
		width: 6.60rem;
		margin: 0 auto;
	}
	.office_bl_03 .image_box img{
		width: 1.90rem;
		height: auto;
	}
	.office_bl_03 .image_box p{
		text-align: left;
		margin: .10rem 0 .30rem .15rem;
		font-size: .18rem;
	}
	.office_bl_03 .description{
		width: 11.50rem;
	    margin: 0 auto;
	    font-size: .18rem;
	}
	.office_bl_03 .graybox{
		width: 11.50rem;
	    padding: .20rem;
	    background: #f4f4f4;
	    border: .01rem solid #c9c9c9;
	    margin: 0 auto .30rem auto;
	}
	.layout_img_r{
	    display: block;
	    width: 10.90rem;
	    margin: 0 auto;
	}
	.layout_img_r img{
		width: 2.80rem;
		height: auto;
	}
	.layout_img_r::after{
		margin-top: .25rem;
		margin-bottom: .25rem;
		content: " ";
		display: block;
		height: .05rem;
		background: white;
	}
	.layout_img_r:last-child:after{
	  	display: none;
	}
	.layout_img_r div.txt{
	    display: inline-block;
	    vertical-align: top;
	    width: 7.42rem;
	    margin-bottom: .10rem;
	}
	.layout_img_r div.txt p{
	    font-size: .15rem;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 .05rem 0;
		margin: 0;
		border: none;
	}
	.office_bl_04 .layout_text,
	.office_bl_04 .layout_text_01{
		width: 11.50rem;
	    margin: 0 auto;
	}
	.office_bl_04 .layout_text h4{
		font-size: .22rem;
		color: #00b67f;
		line-height: 1;
		padding-bottom: .20rem;
	}
	.office_bl_04 .layout_text p{
		font-size: .15rem;
		color: #222222;
		line-height: 1.5;
		padding-bottom: .46rem;
	}
	.office_bl_04 .layout_text_01 p{
		font-size: .18rem;
		color: #222222;
		line-height: 1.5;
		padding-bottom: .20rem;
	}
	/*end of office*/

	/*hospital*/
	.service_hospital .tabs >ul{
		width: 4.60rem;
	}
	.service_hospital .elderly_bl_01,
	.service_hospital .elderly_bl_05{
		padding: 0 0 1.00rem 0;
	}
	.meal_offer_eg{
		width: 4.00rem;
		margin: 0 auto;
		padding: 1.00rem 0 .45rem 0;
	}
	.meal_offer_eg img{
		width: 100%;
		margin: 0 0 .10rem 0;
	}
	.meal_offer_eg label{
		font-size: .18rem;
		line-height: 1;
		color: #222222;
	}
	#hospital02 .elderly_bl_01{
		padding: 1.00rem 0 .45rem 0;
	}
	#hospital02 .mealoffer_ul{
		width: 11.70rem;
		margin: 0 auto .30rem auto;
	}
	.hospital02_mealoffer h4{
		width: 11.50rem;
		font-size: .22rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		margin: 0 auto .10rem auto;
	}
	#hospital02 .meal_offer_div{
		width: 3.60rem;
		margin: 0 auto;
	}
	#hospital02 .meal_offer_div img{
		width: 100%;
	}
	#hospital02 .meal_offer_div p{
		font-size: .18rem;
		line-height: 1;
		color: #222222;
		margin: .10rem 0 0 0;
	}
	#hospital02 .mealoffer_ul_01{
		width: 7.80rem;
	}
	/*end of hospital*/

	/*service*/
	.service{
		width: 11.50rem;
		margin: 1.00rem auto;
	}
	.service_ul{
		margin: 0 0 1.00rem 0;
	}
	.service_ul li img{
		width: 100%;
	}
	.service_three_major{
		margin: .60rem 0 0 0;
	}
	.service_three_major p{
		font-size: .18rem;
		line-height: 1.5;
		color: #222222;
	}
	.service_three_major label{
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		display: block;
		margin: .45rem 0 .20rem 0;
	}
	.service .img_service_06{
		width: 7.60rem;
		height: auto;
		margin: .45rem auto 0 auto;
		display: block;
	}
	/*end of service*/

	/*annual_events*/
	.annual_events{
		width: 11.50rem;
		margin: 1.00rem auto;
	}
	.annual_events ul{
		width: 100%;
		display: flex;
		flex-wrap: wrap;
	}
	.events_div{
		width: 3.60rem;
		margin: 0 auto .40rem auto;
		text-align: center;
	}
	.events_div img{
		width: 100%;
		height: auto;
	}
	.events_div label{
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		display: block;
		margin: .15rem 0 .10rem 0;
	}
	.events_div span{
		font-size: .18rem;
		line-height: 1;
		color: #222222;
	}
	/*end of annual_events*/

	/*event*/
	.event{
		padding: 1.00rem 0;
	}
	.event_div_01{
		background: #defcf3;
		padding: .70rem 0 0 0;
	}
	.event ul{
		width: 11.80rem;
		margin: 0 auto;
		padding: 0 0 .50rem 0;
	}
	.event ul img{
		width: 3.60rem;
		height: auto;
		margin: 0 auto;
		display: block;
	}
	.event p{
		font-size: .24rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .10rem 0;
		width: 11.50rem;
		margin: 0 auto;
	}
	.event_div_02{
		padding: .50rem 0;
	}
	.event ul.event_div_02_ul{
		display: block;
		width: 11.50rem;
	}
	.event ul.event_div_02_ul li{
		width: auto;
		display: inline-block;
		margin: 0 .30rem 0 0;
	}
	.event_div_02_ul .img_width{
		width: 1.90rem;
		height: auto;
	}
	/*end of event*/
}
/*For SP*/
@media only screen and (max-width: 767px){
	
	.h3_common{
		display: block;
		text-align: center;
		margin: 0 0 .50rem 0;
	}
	.h3_common h3{
		font-size: .32rem;
		line-height: 1.3;
		font-weight: bold;
		color: #222222;
		display: inline-block;
		position: relative;
	}
	.h3_common h3:after{
		content: " ";
		position: absolute;
		background: #00b67f;
		width: .50rem;
		height: .04rem;
		left: 50%;
		bottom: -.15rem;
		transform: translate(-50%);
	}
	/*table01*/
	.table01{
		width: 100%;
		margin: 0 auto .20rem auto;
	}
	.table01 th{
		width: 100%;
		padding: .15rem;
		text-align: center;
		vertical-align: top;
		font-weight: bold;
		border-bottom: none;
		font-size: .255rem;
		line-height: 1.5;
		display: block;
	    background: #f0f0f0;
		border-top: .01rem dotted #ccc;
	}
	.table01 td{
		width: 100%;
		padding: .15rem;
		background:#fff;
		border-bottom: none;
		border-top: .01rem dotted #ccc;
		line-height: 1.5;
		font-size: .255rem;
		line-height: 1.5;
		vertical-align: top;
		display: block;
	}
	.table01 td table td{
		padding:0;
		border:none;
		line-height: 1.5;
		display: table-cell;
		width: auto;
		font-size: .255rem;
	}
	.table01 .staff td{
		text-align:right;
		border: none;
		padding-right: 0;
		min-width: 60px;
	}
	/*end of table01*/

	/*table03*/
	.table03{
		width: 7.40rem;
		margin: 0 auto .20rem auto;
	}
	.table03 th{
		padding: .12rem;
		font-weight: bold;
		background: #eee;
		border: .01rem solid #ddd;
		text-align: left;
		font-size: .255rem;
		line-height: 1;
	}
	.table03 td{
		padding: .10rem;
		border: .01rem solid #ddd;
		width: 4.20rem;
		font-size: .255rem;
		line-height: 1;
	}
	.table03 td:last-child{
		width: 3.20rem;
	}
	/*end of table03*/
	ul.bxslider li img.bxpc{
		display: none;
	}
	ul.bxslider li img.bxsp{
		display: block;
	}
	#container{
		min-width: 100%;
		display: block;
	}
	.inner{
	    max-width: 7.10rem;
	    width: 100%;
	    margin: 0 auto;
	    font-size: .12rem;
	}
	.title_area{
		min-width: 100%;
		background: #ededed;
	}
	.title_areain{
		width: 100%;
		margin: 0 auto;
		padding: 0;
		vertical-align: middle;
		box-sizing: border-box;
	}
	.title_area .title01{
		float: left;
		padding: .7rem 0 .7rem 0;
	}
	.title_area .title01 h2{
		font-size: .28rem;
		font-weight: bold;
		/* border-left: .2rem solid #00b580; */
		padding: 0 .1rem;
	}
	.title_area .title01 h2 span{
		display: block;
		font-size: .255rem;
		font-weight: bold;
	}
	.title_area .common_logo{
		width: 100%;
		float: left;
		margin: 0 auto;
	}
	.title_area .common_logo img{
		width: 100%;
		height: 240px;
	}

	/*header and slider*/
	.header{
		min-width: 100%;
		position: fixed;
	}
	.headerin{
		width: 100%;
		max-width: 100%;
		border: none;
	}
	.headerin .logo{
        display: flex;
        width: 40%;
        margin: 0 auto;
        height: 72px;
        align-items: center;
	}
	.headerin .logo img{
		width: 3rem;
		height: auto;
	}
	.headerin .top_right{
		padding-top: 0;
        width: 60%;
        height: 72px;
	}
	.headerin .top_right .sitemap{
		display: none;
	}
	
	.top_right .sitemap, .top_right .inquiry, .top_right .fb{
		margin-right: 8px;
	}
	
	.top_right .insta{
		margin-right: 32px;
	}

	.nav{
		min-width:100%;
	}
	.nav div.navin{
		width: 2.6rem;
		display: inline-block;
		text-align: left;
	}
	.nav div.navin .menu_logo_img{
		width: 100%;
		display: none;
		margin: 0 0 0 auto;
		background: #fff;
		box-sizing: border-box;
	}
	.menu_logo{
		height: 56px;
		margin-left: 16px;
		/* text-align: center; */
	}
	.nav div.navin ul li a{
		padding-top:5px;
		padding-bottom:5px;
		display: inline-block;
		text-align: left;
	}
	.drawr{
	    display: none;
		position: fixed;
	    top: 72px;
		left: 0;
		height: 100% !important;
		max-height: 100% !important;
	    width: 100%;
	    z-index: 9999;
		overflow-x: hidden;
		overflow-y: auto;
		font-size: .18rem;
		color: #333;
	}
	.drawr ul{
	  display: block;
	  margin: 0;
	  padding: .35rem 0 .35rem 0;
	  background:#fefefe;
	  /* border-top: .01rem solid #fff; */
	  padding: 0 16px;
	}
	.drawr ul li ul{
		padding: 0;
	}
	.drawr ul li{
	  display: block;
	  margin: 0;
	  line-height: .4rem;
	  background: #DDE3EF;
	  position: relative;
	  border-left: none !important;
	}
	.drawr ul li label{
		position: absolute;
		top: .2rem;
		right: .18rem;
		width: .2rem;
		height: .2rem;
		background: transparent url(../images/icon_add.png) no-repeat center !important;
		transform: rotate(150deg);
		cursor: pointer;
	}
	.nav div.navin ul .menu-item-has-children.opened label{
		background: transparent url(../images/btn_menu_close.png) #fff no-repeat center !important;
		/* position: absolute; */
		/* z-index: 9999; */
	}
	.drawr ul li:last-child label{
		background: none !important;
	}
	.drawr ul li:hover, .drawr ul li.active, .drawr ul li.sidr-class-active{
	  border-top: 0;
	  line-height: .41rem;
	}
	.drawr ul li a{
	  display: block;
	  padding:0 .15rem;
	  line-height:.6rem;
	  font-size:.25rem !important;
	  font-weight:bold;
	  text-decoration: none;
	}
	.drawr ul li ul{
		display: none;
	}
	.drawr ul li ul li ul{
		display: block;
	}
	.drawr ul li span{
		display:block;
		line-height:.2rem;
		color: #333333;
	}
	.drawr ul li ul{
		border-bottom: 0;
		margin-top: 0 !important;
		position: fixed !important;
		opacity: 1 !important;
		padding: 0 !important;
		top: 72px !important;
	}
	.drawr ul li ul li{
	  line-height: .6rem;
	  font-size: .25rem;
	  background: #fff;
	}
	.drawr ul li ul li label,
	.nav div.navin ul .menu-item-has-children.opened ul li label{
		background: none !important;
	}
	.drawr ul li ul li a{
		background: none !important;
	}
	.drawr ul li ul li:last-child{
	  border-bottom: none;
	}
	.drawr ul li ul li:hover, .drawr ul li ul li.active, .drawr ul li ul li.sidr-class-active{
	  border-top: 0;
	  line-height: .26rem;
	}
	.drawr ul li ul li a,
	.drawr ul li ul li span{
	  color: rgba(51, 51, 51, 0.8);
	  padding-left: .35rem;
	  color:#444 !important;
	  font-size:.25rem !important;
	  font-weight:normal;
	}
	.drawr ul li ul li a{
		display: block !important;
	}
	.drawr ul li ul li ul li a{
		padding-left: .30rem;
	}

	.nav{
		min-width:100%;
	}
	.nav div.navin{
		width: 100%;
		height: 100%;
		display: block;
		text-align: left;
		background: #fff;
		/* margin-top: 72px; */
	}
	.nav div.navin ul li{
		width: 100%;
		line-height:.6rem !important;
		text-align: left;
		border-right: .01rem solid #ffffff !important;
		border-bottom: .01rem solid #ffffff;
		background: none;
		text-align: center;
		border-right: none;
	}
	.nav div.navin ul li a{
		padding-top:.05rem;
		padding-bottom:.05rem;
		padding: 0;
		display: inline-block;
	}

	#fade_in li:first-child:hover ul{
		background: #fff;
	}
	
	.dropmenu li:hover{
		/* background: unset !important; */
	}
	.dropmenu li> a:hover, .dropmenu li.current-page-parent >a, .dropmenu li.current-menu-item >a, .dropmenu li.current-menu-ancestor > a{
    	color: #00b680 !important;
	}
	.sidr-btn{
	    background: transparent url(../images/btn_menu_open.png) no-repeat 0 0;
	    background-size: .50rem .40rem;
	    display: block;
	    width: .50rem;
	    height: .40rem;
		position: fixed;
	    top: .30rem;
	    right: .30rem;
	    cursor: pointer;
	    z-index: 9999999;
	}
	.peke{
		background: transparent url(../images/btn_menu_close.png) no-repeat 0 0;
		background-size: .25rem;
		right: 0rem;
		top: .4rem;
	}
	.bx-wrapper .bx-pager.bx-default-pager a{
		width: 1.12rem !important;
		background: #555353 !important;
		height: .10rem !important;
		padding: 0 !important;
		margin: .05rem !important;
	}
	.bx-wrapper .bx-pager{
		max-width: 100%;
		box-sizing: border-box;
	}
	.bx-wrapper .bx-pager,
	.bx-wrapper .bx-controls-auto{
		bottom: .12rem !important;
	}
	.bx-wrapper .bx-pager .bx-pager-item,
	.bx-wrapper .bx-controls-auto .bx-controls-auto-item{
		padding-bottom: .20rem;
	}
	.bx-wrapper .bx-controls-direction a{
		width: .54rem;
		height: .54rem;
	}
	.bx-wrapper .bx-prev{
		background: url(../images/bx_left_arrow.png) no-repeat right center;
		background-size: .54rem;
	}
	.bx-wrapper .bx-next{
		background: url(../images/bx_right_arrow.png) no-repeat right center;
		background-size: .54rem;
	}
	.bx-wrapper .bx-prev:hover,
	.bx-wrapper .bx-next:hover{
		background-position: right center;
	}
	.menu_address{
		width: 100%;
		background: #fff;
		/* padding: 1.10rem 0 1.35rem 0; */
		margin: 0;
		position: absolute;
		bottom: 0;
	}
	.menu_address_inner{
		/* width: 3.75rem; */
		/* margin: 0 auto; */
		/* text-align: center; */
		display: none;
	}
	.menu_address_inner li{
		padding: .1rem;
		display: inline-block;
		float: left;
		width: 50%;
	}
	.menu_address_inner li img{
		width: 100%;
	}
	.btn_menu_contact,
	.btn_menu_sitemap{
		width: 3.51rem;
		height: auto;
		display: none;
	}
	.btn_menu_phone{
		width: 3.54rem;
		height: auto;
		display: none;
	}
	.btn_menu_sitemap{
		margin: .18rem 0 .36rem 0;
	}
	.menu_address ul{
		width: 2.50rem;
		margin: .35rem auto 1.00rem auto;
		background: none;
		border: none;
	}
	.menu_address_txt{
		width: 100%;
		margin: 0 auto .5rem auto;
		text-align: center;
		line-height: 1.5;
		font-size: .23rem;
		display: none;
		color: #ffffff;
	}
	.menu_address_txt a{
		color: #ffffff;
	}
	.menu_address .footer_copyright{
		background: #b7b7b7;
		border: none;
		padding: .2rem;
		bottom: 0;
		line-height: 1.5;
		font-size: .18rem;
		color: #fff;
	}
	.menu_social{
		display: inline-block;
		margin: .5rem auto;
		text-align: center;
	}
	.menu_fb,
	.menu_ig{
		display: inline-block;
		float: left;
		margin-right: .2rem;
	}
	/*end of header and slider*/

	/*news_area*/
	.news_area{
		min-width:100%;
	}
	.news_areain{
		max-width:7.10rem;
		margin:0 auto;
		padding:.3rem 0;
	}
	.news_areain h3{
		color: #333333;
		font-family: Noto Sans CJK JP;
		font-size: .28rem;
	}
	div.topics-inc{
		height: 6rem;
		padding:.25rem .25rem 0 0;
		overflow:auto;
	}
	ul.news{
		border-bottom:.01rem dotted #444;
		overflow: hidden; /* MacIE */
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		grid-template-rows: repeat(2, 1fr);
		grid-column-gap: 0px;
		grid-row-gap: 0px;
	}
	.row1 { grid-area: 1 / 1 / 3 / 2; }
	.category-name { 
		grid-area: 1 / 2 / 2 / 4; 
		margin: 0;
	}
	.row2 { 
		grid-area: 2 / 2 / 3 / 4; 
		margin: 0 !important;
	}
	ul.news:first-child{
		border-top: .01rem dotted #444;
	}
	/* NN7 */
	ul.news:after{
		content: "";
		display: block;
		clear: both;
		height: .01rem;
		overflow: hidden;
	}
	/* IE6 */
	* html ul.news{
		height: 1em;
		overflow: visible;
	}
	.row1{
		float:left;
		width:1.6rem;
		margin:.1rem 0;
		position:relative;
		padding-left:.15rem;
		font-size: .255rem;
		font-family: Noto Sans CJK JP;
	}
	.row2{
		float:right;
		width:5rem;
		margin:.1rem 0;
		line-height: 1.5;
		font-size: .255rem;
	}
	.row2 p{
		font-size: .2rem;
		font-family: Noto Sans CJK JP;
		color: #333333;
		line-height: 1.5;
	}
	.row2 p a{
		color: #00b680;
	}
	.row2 p a:hover{
		color: #808080;
	}
	.row2 span{
		font-size: .255rem;
	}
	div.topics-inc::-webkit-scrollbar-track
	{
		background-color: #f2f2f2;
	}

	div.topics-inc::-webkit-scrollbar
	{
		width: .1rem;
		background-color: #f2f2f2;
	}

	div.topics-inc::-webkit-scrollbar-thumb
	{
		background-color: #cccccc;
	}
	/*end of news_area*/

	/*contact_area*/
	.contact_area{
		width: 100%;
		min-width: 100%;
		margin: .3rem auto;
		text-align: center;
	}
	.contact_area_inner{
		margin: 0 auto;
		padding: 20px 0;
	}
	.contact_area .contact_area_inner .contact_tel{
		width: 3.1rem;
		height: 1rem;
		display: inline-block;
		margin-right: .25rem;
	}
	.contact_area .contact_area_inner .contact_tel.only_pc{
		display: none;
	}
	.contact_area .contact_area_inner .contact_inq{
		display: inline-block;
	}
	.contact_area .contact_area_inner .contact_tel img,
	.contact_area .contact_area_inner .contact_inq img{
		width: 100%;
		height: 100%;
		vertical-align: unset;
	}
	/*end of contact_area*/

	/*footer*/
	.footer{
		min-width: 100%;
		background: #4d4d4d;
	}
	.footerin{
		max-width: 7.10rem;
		margin: 0 auto;
		padding: .25rem 0;
		text-align: left;
		box-sizing: border-box;
	}
	.footerin ul{
		width: 48%;
		vertical-align: top;
		text-align: left;
	}
	#menu-footer-menu1,
	#menu-footer-menu3,
	#menu-footer-menu4{
		padding: 0;
	}
	.footerin ul li ul{
		width: 100%;
		padding: .13rem 0;
		box-sizing: border-box;
	}
	.footerin ul li ul li ul{
		padding: .1rem 0 0 0;
	}
	.footerin .heading01,
	.heading01_02{
		font-size: .20rem;
		line-height: 1;
		color: #adc743;
		padding: 0 0 .15rem 0;
		margin: 0;
	}
	.heading01_02{
		padding: .15rem 0 .1rem 0;
		margin: 0 0 .1rem 0;
	}
	.footerin .heading01 a,
	.heading01_02 a{
		font-family: Noto Sans CJK JP;
		font-size: .20rem;
		line-height: 1.5;
		color: #fff;
	}
	.footerin ul li.heading02,
	.footerin ul li.heading03{
		position: relative;
		padding: 0 0 .1rem .13rem;
	}
	.heading01 a,
	.heading02 a,
	.heading03 a{
		color: #fff;
		font-size: .20rem;
		font-family: Noto Sans CJK JP;
		line-height: 1;
	}
	.heading01 a:hover,
	.heading02 a:hover,
	.heading03 a:hover{
		color: #00b680;
	}
	.footerin ul li.heading03{
		padding: 0 0 .1rem .13rem;
	}
	.footerin ul li.heading03:last-child{
		padding-bottom: 0;
	}
	.footerin .footer_address{
		width: 100%;
		margin: 0 0 .15rem 0;
	}
	.footerin .footer_address,
	#menu-footer-menu1,
	#menu-footer-menu3{
		padding: 0 0 .25rem .2rem;
	}
	#menu-footer-menu2,
	#menu-footer-menu4{
		padding: 0 0 .25rem .5rem;
	}
	.footer_address .social_btn{
		font-size: .12rem;
	}
	.footer_address .fb_btn a,
	.footer_address .insta_btn a{
		display: inline-block;
		float: left;
		margin-right: .2rem;
	}
	.footer_address li img{
		width: .3rem;
		height: .3rem;
	}
	.fa-facebook-f{
		color: #ffffff;
	    background: #1778f2;
	    font-size: .25rem;
	    padding: .13rem .2rem;
	    border-radius: 50%;
	}
	.insta_btn{
	  display: inline-block;
	  text-align: center;
	  color: #2e6ca5;
	  font-size: .3rem;
	  text-decoration: none;
	}
	.insta_btn:hover{
	  color:#668ad8;
	  transition: .5s;
	}
	.insta_btn .insta{
	  position: relative;
	  display: inline-block;
	  width: .48rem;
	  height: .48rem;
	  background: -webkit-linear-gradient(135deg, #427eff 0%, #f13f79 70%) no-repeat;
	  background: linear-gradient(135deg, #427eff 0%, #f13f79 70%) no-repeat;
	  overflow: hidden;
	  border-radius: .13rem;
	}
	.insta_btn .insta:before{
	  content: '';
	  position: absolute;
	  top: .23rem;
	  left: -.18rem;
	  width: .48rem;
	  height: .48rem;
	  background: -webkit-radial-gradient(#ffdb2c 10%, rgba(255, 105, 34, 0.65) 55%, rgba(255, 88, 96, 0) 70%);
	  background: radial-gradient(#ffdb2c 10%, rgba(255, 105, 34, 0.65) 55%, rgba(255, 88, 96, 0) 70%);
	}
	.insta_btn .fa-instagram{
	  color: #FFF;
	  position: relative;
	  z-index: 2;
	  font-size: .3rem;
	  line-height: .48rem;
	}
	#onhover{
	 	display: none;
	}
	#group_img_change:hover img[id="initial"]{
	 	display: none;
	}
	#group_img_change:hover img[id="onhover"]{
	 	display: block;
	}
	.footer_address_txt{
		font-size: .20rem;
		font-family: Noto Sans CJK JP;
		line-height: 1.8;
		color: #fff;
		margin: 0 0 .2rem 0;
	}
	.footer_address_txt a{
		color: #ffffff;
	}
	.footer_copyright{
		width: 100%;
		/* max-width: 7.10rem; */
		margin: 0;
		border-top: .03rem solid #787878;
	}
	.footer_copyright p{
		width: 4.6rem;
		font-size: .18rem;
		line-height: 1.2;
		color: #ffffff;
		font-family: Noto Sans CJK JP;
		text-align: center;
		margin: .15rem auto 0 auto;
		padding: .15rem;
		border: none;
	}
	.pagetop{
		position: fixed;
		z-index:100;
		bottom: -.5rem;
		right: .2rem;
	}
	.pagetop a{
		display: block;
		width: .5rem;
		height: .5rem;
		background:#ccc;
	}
	.pagetop a:after{
		display: block;
		content: "";
		position: absolute;
		top: 50%;
		left: 40%;
		width: .06rem;
		height: .06rem;
		margin: -.04rem 0 0 0;
		border-top: solid .03rem #fff;
		border-right: solid .03rem #fff;
		-webkit-transform: rotate(-45deg);
				transform: rotate(-45deg);
	}
	.pagetop a:hover{
		background:#00b67f;
	}
	/*end of footer*/

	.scrollNav{
		position: fixed;
		top: -.42rem;
		left: 0;
		min-width:100%;
		background:#64a820;
		opacity:0.9;
		box-shadow:0 .03rem .05rem 0 #777;
		-moz-box-shadow:0 .03rem .05rem 0 #777;
		-webkit-box-shadow:0 .03rem .05rem 0 #777;
		-o-box-shadow:0 .03rem .05rem 0 #777;
		-ms-box-shadow:0 .03rem .05rem 0 #777;
		z-index:200;
		display: none;
	}
	.scrollNav ul{
		max-width:7.10rem;
		text-align:center;
		margin:0 auto;
	}
	.scrollNav ul li{
		display:inline-block;
		padding:0 .2rem;
		font-size:.14rem;
		letter-spacing:.01rem;
		position: relative;
		width:auto !important;
		height:.35rem !important;
		line-height:.35rem !important;
		float:none !important;
		border-left:.01rem solid #a8d57c !important;
	}
	.scrollNav ul li:last-child{
		border-right:.01rem solid #a8d57c !important;
	}
	.scrollNav ul li a{
		text-decoration:none;
		display:block;
		padding:0 !important;
		color:#555353;
		font-weight:bold;
	}
	.scrollNav ul li a:hover{
		color:#ddd !important;
	}
	.scrollNav ul li ul li{
		width: 16rem !important;
		border-left:0 !important;
		border-right:0 !important;

	}
	.scrollNav ul li ul li a{
		border-top: .01rem solid #a8d57c;
		background: #7ec537;
		color:#fff;
		padding:0 .05rem !important;
		text-align:left !important;
	}
	.scrollNav ul li:hover > a{
		background: #64a820 !important;
		color:#fff;
	}
	.scrollNav ul li a:hover{
		background: #64a820 !important;
	}
	ul.bxslider li img.bxpc{
		display: block;
	}
	ul.bxslider li img.bxsp{
		display: none;
	}
	.wrapper{
		min-width:100%;
	}
	/*facebook page*/
	.fb_wrapper{
		padding: .8rem 0;
	}
	.fb_wrapper .content{
		text-align: center;
	}
	.fb-box{
		margin: 0 auto;
		width: 100%;
		padding: 0;
	}
	.fb-page{
		width: 100%;
	    box-sizing: border-box;
	    padding: 0;
	}
	.fb_iframe_widget span{
		margin: .2rem auto;
	}
	/*end of facebook page*/

	/*inquiry page*/
	.inquiry_wrapper{
		padding: .8rem 0;
	}
	.inquiry_inner{
		max-width: 7.10rem;
		margin: 0 auto;
	}
	.inquiry_title01 h3{
		padding: 0 0 .15rem 0;
		font-size: .28rem;
		color: #00b67f;
		font-weight: bold;
	}
	.inquiry_inner .left_col{
		width: 100%;
		display: block;
		box-sizing: border-box;
		padding-right: .07rem;
	}
	.inquiry_inner .right_col{
		width: 100%;
		display: block;
		box-sizing: border-box;
	}
	.inquiry_inner .right_col img{
		width: 100%;
	}
	.inquiry_inner .contact_from_tel,
	.inquiry_inner .contact_from_web{
		margin-bottom: .3rem;
	}
	.inquiry_inner .inq_contact{
		line-height: 2;
		font-size: .17rem;
		color: #555353;
	}
	.inquiry_inner .inq_contact span{
		color: #222222;
		font-weight: bold;
		font-size: .18rem;
		display: block;
		line-height: 1.5;
	}
	.inquiry_inner .inq_contact .contact_phno{
		font-weight: bold;
		font-size: .255rem;
	}
	.inquiry_inner .inq_contact span,
	.inquiry_inner .contact_from_web p{
		font-size: .255rem;
	}
	.contact_from_web .btn_form{
		background: none;
		color: #ff8827;
		font-size: .18rem;
		border: .04rem solid #ffd6a9;
		padding: .15rem;
		font-weight: bold;
		margin-bottom: .4rem;
	}
	.contact_from_web .btn_form:hover,
	.contact_from_web .btn_form:focus{
		background: #ff8827;
		color: #ffffff;
	}
	.contact_from_web .btn_form:focus{
		outline: none;
	}
	.contact_from_web p{
		font-size: .18rem;
		color: #222222;
		line-height: 1.5;
	}
	.modal{
		display: block;
		margin: .6rem auto 0 auto;
		width: 100%;
	}
	/***** table_Form *****/
	.inquiry_content table{
		border-collapse: collapse !important;
	    border-spacing: 0 !important;
	}
	.inquiry_content .table_Form{
		width: 100%;
		margin:0 auto .2rem auto;
	}
	.inquiry_content .table_Form, 
	.inquiry_content .table_Form th, 
	.inquiry_content .table_Form td{
		border-bottom: .01rem solid #fff; 
		border-collapse: collapse;
		font-size: .16rem;
	}
	.inquiry_content .table_Form th{
		width:30%;
		padding: .15rem;
		vertical-align:middle;
		font-weight:bold;
		background: #ededed;
		text-align: left;
	}
	.inquiry_content .table_Form th div{
		position:relative;
		font-weight: bold;
		font-size: .16rem;
	}
	.inquiry_content .table_Form th div img{
		position:absolute;
		top:0;
		right:0;
		width: .4rem;
		vertical-align: middle;
	}
	.inquiry_content .table_Form td{
		padding: .15rem;
		background:#f3f3f3;
	}
	.inquiry_content p{
		line-height: 1.5;
		font-size: .16rem;
	}
	.inquiry_content .table_Form td input{
		width:85%;
		padding:.05rem;
		border: .01rem solid #767676; 
		font-weight: normal;
		margin: .05rem 0;
		font-size: .16rem;
	}
	.inquiry_content .table_Form td label{
		font-size: .15rem;
	}
	.inquiry_content .table_Form td input.wauto{
		width:.15rem;
		margin-right: .05rem;
		vertical-align: middle;
	}
	.inquiry_content .table_Form td textarea{
		width:85%;
		height: 1.5rem;
		padding:.05rem;
		border: .01rem solid #767676; 
		font-weight: normal;
	}
	.form_scroll{
		max-width:5.2rem; 
		overflow:auto; 
		height:1.5rem;
	}
	:focus::-webkit-input-placeholder{ 
		color: white; 
	}
	:focus:-moz-placeholder{ 
		color: white; 
	}
	:focus::-moz-placeholder{
		color: white; 
	}
	:focus:placeholder-shown{
		color: white; 
	}
	.inquiry_content .inquiry_box{
		width:85%;
		font-size:.16rem;
		height:1.65rem;
		padding:.1rem;
		overflow:auto;
	}
	.inquiry_content .inquiry_box li,
	.inquiry_content .inquiry_box p{
		font-weight: normal;
		line-height: 1.5;
	}
	/***** btn_link *****/
	.inquiry_content p.btn_link{
		width:2.3rem;
		height:.4rem;
		margin:0 auto .2rem auto;
	}
	.inquiry_content p.btn_link a{
		display:block;
		width:100%;
		height:100%;
		color:#fff;
		font-size:.16rem;
		font-weight:bold;
		text-align:center;
		vertical-align:middle;
		text-decoration:none;
		padding-top:.13rem;
		background:#ff8400;
		border:.04rem solid #ffd4a5;
		border-radius:.05rem;
	}
	.inquiry_content p.btn_link a:hover{
		background:#ff6c00;
		color:#fff;
	}
	.inquiry_content p.btn_link input{
		display:block;
		width:100%;
		height:.6rem;
		color:#fff;
		font-size:.16rem;
		font-weight:bold;
		text-align:center;
		vertical-align:middle;
		text-decoration:none;
		background:#ff8400;
		border:.04rem solid #ffd4a5;
		border-radius:.05rem;
	}
	.inquiry_content p.btn_link input:hover{
		background:#ff6c00;
	}
	.inquiry_content p.btn_link_back{
		width:2.3rem;
		height:.4rem;
		margin:0 auto .3rem auto;
	}
	.inquiry_content p.btn_link_back input{
		display:block;
		width:100%;
		height:.6rem;
		color:#fff;
		font-size:.16rem;
		font-weight:bold;
		text-align:center;
		vertical-align:middle;
		text-decoration:none;
		background:#aaa;
		border:.04rem solid #ccc;
		border-radius:.05rem;
	}
	.inquiry_content p.btn_link_back input:hover{
		background:#888;
	}
	.inquiry_content p.btn_link input:focus,
	.inquiry_content p.btn_link_back input:focus{
		outline: none;
	}
	.inquiry_content .red{
	    color: #a50918;
	    margin: .05rem 0 0 0;
	}
	/*end of inquiry page*/

	/*privacy page*/
	.privacy_inner{
		margin-bottom: .2rem;
		padding: .8rem 0;
	}
	.privacy_content h3{
		padding: 0 0 .15rem 0;
		font-size: .28rem;
		color: #00b67f;
		font-weight: bold;
	}
	.privacy_content ol.ol_list{
	    counter-reset: my-counter;
	    padding: 0;
	    margin-bottom: .3rem;
	    list-style: none;
	}
	.privacy_content ol.ol_list span{
		font-size: .255rem;
	}
	.privacy_content ol.ol_list li{
	    margin-bottom: .2rem;
	    padding-left: .4rem;
	    padding-right: .1rem;
	    position: relative;
	    line-height: 1.5;
	    color: #222222;
	    font-size: .255rem;
	}
	.privacy_content ol.ol_list li:before{
	    content: counter(my-counter);
	    counter-increment: my-counter;
	    background-color: #00b67f;
	    color: #fff;
	    display: block;
	    float: left;
	    line-height: .35rem;
	    margin-left: -.4rem;
	    text-align: center;
	    height: .3rem;
	    width: .3rem;
	}
	.privacy_content p.text_right{
	    text-align: right;
	    padding-right: .1rem;
	    line-height: 1.5;
	    font-size: .255rem;
	}
	.privacy_content .bold{
	    font-weight: bold;
	    font-size: .255rem;
	}
	.privacy_content ol.ol_list li ul{
	    counter-reset: list;
	    margin-top: .15rem;
	    margin-left: .05rem;
	    list-style: none;
	}
	.privacy_content ol.ol_list li ul li:before{
	    counter-increment: list;
	    content: counter(list);
	    font-weight: normal;
	    border-radius: 5rem;
	}
	.privacy_content .circle_green{
	    background-color: #00b67f;
	    color: #fff;
	    padding: 0 .06rem;
	    margin: 0 .02rem;
	}
	.privacy_content a{
		color: #00b67f;
		text-decoration: underline;
	}
	.privacy_content a:hover{
		opacity: .7;
	}
	/*end of privacy page*/

	/*sitemap page*/
	.sitemap_content{
		padding: .8rem 0;
	}
	.sitemap_content h4{
	    position: relative;
	    font-size: .26rem;
	    font-weight: bold;
	    padding: 0 0 0 .05rem;
	    border-left: .06rem solid #ccc;
	    margin-bottom: .1rem;
	    color: #00b680;
	}
	.sitemap_content h4:before{
	    position: absolute;
	    left: -.06rem;
	    bottom: 0;
	    content: '';
	    width: .06rem;
	    height: 50%;
	    background-color: #00b680;
	}
	.sitemap_content h4:after{
	    position: absolute;
	    left: 0;
	    bottom: 0;
	    content: '';
	    width: 100%;
	    height: 0;
	}
	.sitemap_box{
	    width: 100%;
	    display: inline-block;
	    vertical-align: top;
	    margin: 0 auto;
	    padding: 0 .15rem;
	}
	.sitemap_box ul{
		list-style: none;
	}
	.sitemap_box ul li.heading01{
	    position: relative;
	    padding-left: .2rem;
	    margin: .1rem 0;
	    font-weight: bold;
	    line-height: .3rem;
	    border-bottom: dotted .02rem #00b680;
	    color: #00b680;
	    font-size: .255rem;
	}
	.sitemap_box ul li.heading02{
	    position: relative;
	    padding-left: .25rem;
	    margin-bottom: .05rem;
	    font-size: .12rem;
	}
	.sitemap_box ul li.heading03{
	    position: relative;
	    padding-left: .3rem;
	    margin-bottom: .05rem;
	    font-size: .12rem;
	}
	.sitemap_content h4 a,
	.sitemap_box ul li.heading01 a,
	.sitemap_box ul li.heading02 a,
	.sitemap_box ul li.heading03 a{ 
	    color: #00b680;
	    text-decoration: underline;
	    font-size: .255rem;
    	line-height: 1.3;
	}
	.sitemap_content h4 a:hover,
	.sitemap_box ul li.heading01 a:hover,
	.sitemap_box ul li.heading02 a:hover,
	.sitemap_box ul li.heading03 a:hover{
		opacity: .7;
	}
	.sitemap_box ul li.heading01:before{
	    counter-increment: list;
	    content: "";
	    display: block;
	    position: absolute;
	    left: 0;
	    height: .15rem;
	    width: .15rem;
	    border-radius: 5rem;
	    background: #00b680;
	    top: 50%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.sitemap_box ul li.heading01:after{
	    content: "";
	    display: block;
	    position: absolute;
	    left: .06rem;
	    height: 0;
	    width: 0;
	    border-top: .06rem solid transparent;
	    border-bottom: .06rem solid transparent;
	    border-left: .07rem solid #fff;
	    top: 50%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.sitemap_box ul li.heading02:before{
	    content: '';
	    display: block;
	    position: absolute;
	    top: .13rem;
	    left: .13rem;
	    height: 0;
	    width: 0;
	    border-top: .06rem solid transparent;
	    border-right: .07rem solid transparent;
	    border-bottom: .06rem solid transparent;
	    border-left: .09rem solid #00b680;
	}
	.sitemap_box ul li.heading03:before{
	    content: '';
	    height: .01rem;
	    width: .1rem;
	    display: block;
	    background: #00b680;
	    position: absolute;
	    top: .11rem;
	    left: .16rem;
	}
	/*end of sitemap page*/

	/* CSS for volunteer page */
	.volunteer{
		padding: .8rem 0;
	}
	.top_content02_volunteer{
	    max-width: 7.10rem;
	    margin: 0 auto;
	    padding: .5rem 0;
	}
	.volunteer .imgRight{
	    float: right;
	    margin: 0 0 .3rem .2rem;
	    line-height: 0;
	}
	.volunteer .imgLeft{
	    float: left;
	    margin: 0 .2rem .3rem 0;
	    line-height: 0;
	}
	.volunteer_area{
	    min-width: 100%;
	    min-height: 4.5rem;
	    background: url(../volunteer/images/bg_volunteer.gif);
	    background-position: center center;
	    background-size: cover;
	}
	.top_content02_volunteer,
	.volunteer_areain{
	    max-width: 7.10rem;
	    margin: 0 auto;
	    padding: .3rem 0;
	}
	.top_content02_volunteer{
		padding-bottom: 0;
	}
	.volunteer_areain h4{
	    position: relative;
	    font-size: .255rem;
	    font-weight: bold;
	    color: #00b67f;
	    padding: 0;
	    margin-bottom: .1rem;
	}
	.volunteer_areain .image_box{
	    text-align: center;
	    margin-bottom: .3rem;
	}
	.volunteer_areain .image_box li{
	    display: inline-block;
	    vertical-align: middle;
	    text-align: left;
	    padding: 0 .1rem;
	    margin-bottom: .2rem;
	}
	.volunteer_areain .zoom{
	    overflow: hidden;
	}
	.top_content02_volunteer .h3_common h3:after{
		left: 30%;
	}
	.top_content02_volunteer .cnt_01,
	.top_content02_volunteer .cnt_02,
	.top_content02_volunteer .cnt_03{
		margin-bottom: .5rem;
	}
	.top_content02_volunteer h3{
		font-size: .28rem;
		font-weight: bold;
	}
	.top_content02_volunteer .cnt_01 .imgRight{
		width: 40%;
	}
	.top_content02_volunteer .cnt_01 .txt_content{
		width: 55%;
		float: left;
		font-size: .255rem;
	}
	.top_content02_volunteer .cnt_01 p span{
		font-size: .255rem;
		line-height: 1.5em;
	}
	.top_content02_volunteer .cnt_02 .imgLeft{
		width: 30%;
	}
	.top_content02_volunteer .cnt_02 .imgLeft img{
		width: 100%;
	}
	.top_content02_volunteer .cnt_02 .txt_content{
		width: 65%;
		float: left;
	}
	.top_content02_volunteer .cnt_02 p{
		font-size: .255rem;
		line-height: 1.5em;
	}
	.volunteer_areain{
		padding: .5rem 0 0;
	}
	.volunteer_areain h4{
		font-size: .24rem;
		color: #00b67f;
		margin-bottom: .2rem;
		font-weight: bold;
	}
	.volunteer_areain h4:before{
		content: none;
	}
	.volunteer_areain .image_box{
		width: 7.10rem;
		text-align: left;
		margin-bottom: 0;
	}
	.volunteer_areain .image_box li{
		width: 31%;
		padding: 0 .27rem;
		margin-bottom: .6rem;
	}
	.volunteer_areain img{
		width: 100%;
	}
	.volunteer_areain .image_box .first_img{
		padding-left: .27rem;
	}
	.volunteer_areain .image_box .last_img{
		padding-right: .27rem;
	}
	/* End CSS for volunteer page */

	/*service-food page & regional-cuisine*/
	.service_foodinner,
	.regional_inner{
		padding: .8rem 0;
	}
	.bg_eventfood{
	    max-width: 7.10rem;
	    margin: 0 auto;
	    padding: .3rem .4rem;
	/* 	background: url(../service/images/bg_eventfood.jpg); */
	    border-radius: .2rem;
	}
	.bg_eventfood a{
		text-align: left;
		color: #00b67f;
		text-decoration: underline;
		font-size: .255rem;
		vertical-align: middle;
	}
	.bg_eventfood a:hover{
		opacity: .7;
		text-decoration: none;
	}
	.arrow01{
	    position: relative;
	    padding-left: .3rem;
	}
	.arrow01:before{
	    counter-increment: list;
	    content: "";
	    display: block;
	    position: absolute;
	    left: 0px;
	    height: .2rem;
	    width: .2rem;
	    border-radius: 50%;
	    background: #00b67f;
	    top: 55%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.arrow01:after{
	    content: "";
	    display: block;
	    position: absolute;
	    left: .07rem;
	    height: 0;
	    width: 0;
	    border-top: .06rem solid transparent;
	    border-bottom: .06rem solid transparent;
	    border-left: .06rem solid #fff;
	    top: 55%;
	    -moz-transform: translateY(-50%);
	    -webkit-transform: translateY(-50%);
	    -o-transform: translateY(-50%);
	    -ms-transform: translateY(-50%);
	    transform: translateY(-50%);
	}
	.wp-block-image{
	    max-width: 100%;
	    margin: .5rem 0;
	    text-align: center;
	}
	.wp-block-image img{
	    max-width: 100%;
	    width: 60% !important;
	}
	.wp-block-image img.bigger{
		width: 100% !important;
		max-width: 100%;
	}
	dl.accordion{
	    max-width: 100%;
	    margin: .5rem 0 .3rem 0;
	}
	.table_accordion{
	    min-width: 100%;
	    padding: 0;
	    background: #effce2;
	}
	dl.accordion dt{
	    position: relative;
	    height: .45rem;
	    line-height: .45rem;
	    padding-left: .3rem;
	    font-weight: bold;
	    color: #fff;
	    background: #66ad21;
	    border-bottom: .01rem solid #85c942;
	    font-size: .255rem;
	}
	dl.accordion dt:after{
	    display: block;
	    content: "";
	    position: absolute;
	    top: 35%;
	    left: .1rem;
	    width: .08rem;
	    height: .08rem;
	    margin: -.04rem 0 0 0;
	    border-top: solid .03rem #fff;
	    border-right: solid .03rem #fff;
	    -webkit-transform: rotate(135deg);
	    transform: rotate(135deg);
	}
	.table_accordion th{
	    width: 30%;
	    font-weight: bold;
	    vertical-align: top;
	    border-bottom: .01rem dotted #666666;
	    padding: .1rem;
	    font-size: .255rem;
	}
	.table_accordion td{
	    width: 80%;
	    border-bottom: .01rem dotted #666666;
	    padding: .1rem;
	}
	/*end of service-food page & regional-cuisine*/

	/*information*/
	.tabs{
		padding: .85rem 0 .50rem 0;
	}
	.tabs >ul{
		width: 4.60rem;
		margin: 0 auto .50rem auto;
		border: .01rem solid #00b67f;
		border-radius: .20rem;
	}
	.tabs >ul li{
		text-align: center;
		border-right: .01rem solid #00b67f;
	}
	.tabs >ul li:last-child{
		border-right: none;
	}
	.tabs >ul li a{
		font-size: .20rem;
		line-height: 1;
		color: #00b67f;
		padding: .12rem 0;
		display: block;
	}
	.tabs >ul li.ui-tabs-active a{
		background: #00b67f;
		color: #fff;
	}
	.tabs >ul li a:focus{
		outline: none;
	}
	.tabs >ul li:first-child a{
		border-top-left-radius: .20rem;
		border-bottom-left-radius: .20rem;
	}
	.tabs >ul li:last-child a{
		border-top-right-radius: .20rem;
		border-bottom-right-radius: .20rem;
	}
	.information .inner{
		max-width: 90%;
	}
	.information .content{
		width: 100%;
		margin: 0 auto;
	}
	.information #information em{
		font-style: normal;
		font-size: .255rem;
	}
	.information #information a[href^="http"]{
	    background: url(../images/icon_blank.png) no-repeat 100% 50%;
	    padding-right: .15rem;
	}
	.information #information a:link,
	.information #information a{
	    color: #44800a;
	    text-decoration: underline;
	}
	.information #information .table01{
		width: 7.40rem;
	}
	.information #greeting{
		width: 100%;
		margin: 0 auto;
	}
	.information #greeting ul li:first-child{
		padding: 0 .15rem 0 0;
		text-align: right;
	}
	.information #greeting ul li:last-child{
		padding: .40rem 0 0 0;
		text-align: center;
	}
	.information #greeting p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		margin: 0 0 .23rem 0;
		text-align: left;
	}
	.information #greeting p:last-child{
		margin: 0;
	}
	.information #greeting .img_company{
		width: 5.50rem;
		height: auto;
	}
	.information #greeting .img_sign{
		margin: .25rem 0 0 0;
		width: 1.87rem;
		height: auto;
	}
	/*end of information*/

	/*business*/
	.business .content{
	    max-width: 7.40rem;
	    margin: 0 auto .80rem auto;
	}
	#bussiness_tabs.tabs >ul{
		width: 7.40rem;
	}
	.business_ttl_01{
		font-size: .255rem;
		line-height: 1;
		color: #222222;
		display: block;
		text-align: center;
		margin: 0 0 .50rem 0;
	}
	.business #business p,
	#management_tabs #contest ul p,
	#management_tabs #management ul p,
	.service .service_ul li p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 .30rem 0;
	}
	.business .tabs_common ul,
	#management_tabs #contest ul,
	#management_tabs #management >ul{
		width: 7.40rem;
		margin: 0 auto .50rem auto;
	}
	.business .padding_right,
	#management_tabs #contest .padding_right,
	#management_tabs #management .padding_right,
	.service .service_ul .padding_right{
		padding: 0;
	}
	.business .padding_left,
	#management_tabs #management .padding_left{
		padding: .30rem 0 0 0;
	}
	.business #business li img{
		width: 100%;
	}
	.business #business_system p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
	}
	.business #business_system p span{
		font-size: .255rem;
	}
	.imgRight{
	    float: none;
	    width: 3.60rem;
	    margin: 0 auto .40rem auto;
	}
	.imgRight img{
		width: 100%;
	}
	.business #process .content{
		width: 100%;
		margin: 0 auto;
	}
	.business #process .tab-4-ttl{
		font-size: .255rem;
		line-height: 1.3;
		color: #222222;
		padding: 0 0 .25rem 0;
	}
	.process-div{
		width: 5.00rem;
		background: #e6e6e6;
		padding: .20rem;
		margin: 0 0 .80rem 0;
	}
	.process-div-left,
	.process-div-right{
		float: none;
		margin: 0 auto .30rem auto;
	}
	.process-div h5{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
	}
	.process-div p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		padding: .25rem 0 .10rem 0;
		height: auto;
	}
	.process-div img{
		width: 4.20rem;
		height: auto;
		margin: 0 auto;
		display: block;
	}
	.graybox{
	    padding: .30rem;
	    background: #e6e6e6;
	    margin: 0 auto .30rem auto;
	}
	.graybox h4{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .20rem 0;
	}
	.graybox p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		border-bottom: .05rem solid #fff;
		margin: 0 0 .30rem 0;
		padding: 0 0 .30rem 0;
	}
	.graybox p:last-child{
		margin: 0;
		border-bottom: none;
		padding: 0;
	}
	.graybox a{
		color: #00b67f;
		text-decoration: underline;
	}
	#trustsituation p{
		font-size: .255rem;
		line-height: 1.5;
		color: #00b67f;
		font-weight: bold;
	}
	#trustsituation img{
	    width: 100%;
	    height: auto;
	    margin: .80rem auto 0 auto;
	    display: block;
	}
	/*end of business*/

	/*management*/
	#management_tabs >ul{
		width: 7.45rem;
		margin: 0 auto .50rem auto;
	}
	#management_tabs ul li img{
		width: 90%;
		margin: 0 auto;
		display: block;
	}
	#management_tabs #management .human_cap_item{
		width: 100%;
		background: #e6e6e6;
		padding: .25rem 0 .70rem 0;
	}
	#management_tabs .human_cap_item h4{
		text-align: center;
		font-size: .27rem;
		line-height: 1;
		color: #00b680;
		font-weight: bold;
		padding: 0 0 .20rem 0;
	}
	#management_tabs .human_cap_item ul{
		width: 7.40rem;
		margin: 0 auto;
	}
	#management_tabs .human_cap_item ul li{
		text-align: center;
		margin: 0 0 .30rem 0;
	}
	#management_tabs .human_cap_item ul li img{
		width: 2.60rem;
		height: auto;
	}
	#management_tabs #management .list_of_major{
		width: 7.40rem;
		margin: 0 auto;
		padding: .40rem 0;
	}
	#management_tabs #management .list_of_major h4{
		text-align: left;
		font-size: .255rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .20rem 0;
	}
	#management_tabs #new_employee_training img{
		width: 7.40rem;
		height: auto;
		margin: 0 auto;
		display: block;
	}
	#management_tabs #employee_training{
		text-align: center;
	}
	#management_tabs #employee_training .img_employee_training_01{
		width: 2.96rem;
	    height: auto;
	    margin: 0  .30rem 0 0;
	}
	#management_tabs #employee_training .img_employee_training_02{
		width: 2.96rem;
		height: auto;
	}
	#management_tabs #contest .content{
		width: 7.40rem;
		margin: 0 auto;
	}
	#management_tabs #contest >ul li img{
		width: 100%;
	}
	#management_tabs #contest ul.ul_contest_img{
		width: 7.40rem;
		margin: .80rem auto 0 auto;
	}
	#management_tabs #contest ul.ul_contest_img li{
		text-align: center;
		margin: 0 0 .30rem 0;
	}
	#management_tabs #contest ul.ul_contest_img img{
		width: 90%;
		height: auto;
	}
	#management_tabs #cakes_course >p,
	#management_tabs #employee_training >p,
	#management_tabs #new_employee_training >p,
	#management_tabs #management >p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		text-align: center;
		width: 7.40rem;
	    margin: 0 auto .50rem auto;
	}
	#management_tabs #cakes_course .div_bg{
		background: url(../images/bg_cake_course.jpg) top center no-repeat;
		background-size: cover;
		padding: .40rem 0 0 0;
	}
	#management_tabs #cakes_course ul{
		width: 7.40rem;
		margin: 0 auto;
	}
	#management_tabs #cakes_course ul li{
		text-align: center;
		margin: 0 0 .40rem 0;
	}
	/*end of management*/

	/*sitepolicy*/
	.sitepolicy{
		width: 7.40rem;
		margin: 0 auto;
		padding: 1.00rem 0;
	}
	.sitepolicy h3{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .15rem 0;
	}
	.sitepolicy p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 .25rem 0;
	}
	.sitepolicy a{
		font-size: .255rem;
		line-height: 1;
		color: #00b67f;
		text-decoration: underline;
	}
	/*end of sitepolicy*/

	/*school*/
	.school .elderly_bl_01 h5{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .10rem 0;
	}
	.baby_food{
		padding: 1.00rem 0;
		background: #e6e6e6;
		width: 100%;
	}
	.baby_food .inner{
		width: 7.40rem;
		margin: 0 auto;
	}
	.baby_food .table03{
		width: 7.40rem;
		margin: 0 auto;
		background: #fff;
	}
	.baby_food .table03 td:first-child{
		width: 4.45rem;
	}
	.baby_food .table03 td:last-child{
		width: 5.75rem;
	}
	.baby_food .image_ul{
		width: 7.40rem;
		margin: .20rem auto .50rem auto;
	}
	.baby_food .image_ul li{
		margin: 0 0 .30rem 0;
	}
	.baby_food .image_ul img{
		width: 2.95rem;
		margin: 0 auto;
		display: block;
	}
	.baby_food p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
	}
	.food_education_ul{
		width: 100%;
	}
	.food_education_ul li:first-child{
		width: 100%;
		padding: 0;
	}
	.food_education_ul li:last-child{
		width: 100%;
	}
	.food_education_ul img{
		width: 3.60rem;
		margin: .20rem 0 0 0;
	}
	.food_education_ul img.img_school_06{
		width: 2.40rem;
	}
	.school .schedulebox ul li{
		height: 4.30rem;
	}
	/*end of school*/

	/*start of office,elderly*/
	.elderly_bl_01,
	.office_bl_01,
	.hospital_bl_01{	
		padding: 1.00rem 0;
	}
	.elderly_bl_01 ul,
	.office_bl_01 ul,
	.hospital_bl_01 ul{
		margin: 0 auto;
		width: 7.40rem;
	}
	.elderly_bl_01 li:first-child,
	.office_bl_01 li:first-child,
	.hospital_bl_01 li:first-child{
		width: 100%;
		padding-right: 0;
	}
	.elderly_bl_01 li:last-child,
	.office_bl_01 li:last-child,
	.hospital_bl_01 li:last-child{
		width: 100%;
	}
	.elderly_bl_01 li:last-child img,
	.office_bl_01 li:last-child img,
	.hospital_bl_01 li:last-child img{
		width: 6.40rem;
		height: auto;
		display: block;
		margin: 0 auto;
	}
	.elderly_bl_01 .first_content li p,
	.office_bl_01 .first_content li p,
	.hospital_bl_01 .first_content li p{
		color: #222222;
		margin-bottom: .255rem;
		font-size: .255rem;
		line-height: 1.5;
	}
	.elderly_bl_02,
	.office_bl_02,
	.hospital_bl_02{
		padding: 1.00rem 0;
	}
	.elderly_bl_02 ul,
	.office_bl_02 ul,
	.hospital_bl_02 ul{
		padding-top: .20rem;
		margin: 0 auto;
		width: 7.40rem;
	}
	.elderly_bl_02 ul li:first-child,
	.office_bl_02 ul li:first-child,
	.hospital_bl_02 ul li:first-child{
		margin-right: 0;
	}
	.elderly_bl_02 p,
	.office_bl_02 p,
	.hospital_bl_02 p{
		margin: 0 auto;
		width: 7.40rem;
		font-size: .255rem;
		color: #222222;
		line-height: 1.5;
	}
	.elderly_bl_02 li,
	.office_bl_02 li,
	.hospital_bl_02 li{
		width: 50%;
	}
	.elderly_bl_02 li img,
	.office_bl_02 li img,
	.hospital_bl_02 li img{
		width: 95%;
		height: auto;
		display: block;
		margin: 0 auto;
	}
	.elderly_bl_03,
	.office_bl_03{
		padding: 1.00rem 0;
	}
	.elderly_bl_03 h4,
	.office_bl_03 h4,
	.hospital_bl_04 h4{
		padding: 0 0 .20rem 0;
		margin: 0 auto;
		width: 7.40rem;
		font-size: .28rem;
		color: #00b67f;
		line-height: 1;
		font-weight: bold;
	}
	.elderly_bl_03 li img{
		width: 2.65rem;
		height: auto;
		padding-bottom: .16rem;
	}
	.elderly_bl_03 li span{
		font-size: .255rem;
		color: #222222;
		line-height: 1;
	}
	.elderly_bl_03 .image_box{
		text-align: center;
		margin-bottom: .30rem;
		width: 7.40rem;
		margin: 0 auto;
	}
	.elderly_bl_03 .image_box p{
		text-align: left;
		margin-left: .52rem;
    	font-size: .255rem;
    	margin-bottom: .30rem;
	}
	.office_bl_04,
	.elderly_bl_04{
		padding: 1.00rem 0;
	}
	.elderly_bl_04 .image_box li{
		width: 2.20rem;
		display: inline-block;
	}
	.elderly_bl_04 .image_box{
		width: 6.60rem;
		margin: 0 auto .30rem;
	}
	.elderly_bl_04 .fourth_box{
		margin: 0 auto;
		width: 7.40rem;
	}
	.elderly_bl_04 .fourth_box p{
		font-size: .255rem;
		color: #222222;
		line-height: 1.5;
	}
	.elderly_bl_04 .fourth_box h3{
		margin: .54rem 0 .16rem 0;
		font-size: .28rem;
		color: #00b67f;
		line-height: 1;
		text-align: center;
		font-weight: bold;
	}
	.elderly_bl_04 .fourth_box ul li img{
		width: 1.90rem;
	    height: auto;
	    padding-bottom: .09rem;
	}
	.elderly_bl_04 .fourth_box ul li span{
		font-size: .255rem;
		color: #222222;
		line-height: 1;
		white-space: initial;
	}
	.elderly_bl_04 .image_box_02{
		padding-top: .30rem;
	}
	.elderly_bl_04 .image_box_01 img{
		padding-bottom: .11rem;
		width: 4.80rem;
	    height: auto;
	}
	.elderly_bl_04 .image_box_02 img{
		margin-bottom: .08rem;
		width: 1.89rem;
	    height: auto;
	}
	.elderly_bl_05{
		padding: 1.00rem 0;
	}
	.schedulebox,
	.schedulebox01{
		width: 7.40rem;
		margin: 0 auto;
	}
	.schedulebox ul li{
		background: #e6e6e6;
		width: 3.30rem;
		height: 5.80rem;
		margin: 0 auto .30rem auto;
	}
	.schedulebox01 ul li{
		background: #e6e6e6;
		width: 2.65rem;
		height: 4.37rem;
	}
	.schedulein p{
		padding: .20rem .17rem .08rem .17rem;
	}
	.schedulein p span:after{
		content:"\A"; white-space:pre; 
	}
	.elderly_bl_05 ul li em,
	.hospital_bl_04 ul li em{
		font-style: normal;
		width: 3.30rem;
		display: inline-block;
		padding: .20rem .20rem;
		font-size: .28rem;
		font-weight: bold;
		color: #fffefe;
	}
	.elderly_bl_05 div h3 span{
		font-size: .24rem;
		color: #222222;
		line-height: 1;
		font-weight: bold;
	}
	.schedule em{
		background-color: #fc9a9a;
	}
	.schedule_1 em{
		background-color: #4bd1e1;
	}
	.schedule_2 em{
		background-color: #f2974e;
	}
	.schedule_3 em{
		background-color: #8ca0e0;
	}
	.schedule_title{
		font-size: .255rem;
		line-height: 1;
		color: #d95858;
		padding: 0 0 .10rem 0;
		display: block;
	}
	.schedule_desc{
		font-size: .22rem;
		line-height: 1.5;
		color: #222222;
	}
	.schedule_title_01{
		color: #0493a4;
	}
	.schedule_title_02{
		color: #d66911;
	}
	.schedule_title_03{
		color: #44599a;
	}
	.elderly_bl_06{
		padding: 1.00rem 0;
	}
	.elderly_bl_06 p{
		margin: 0 auto;
		width: 7.40rem;
		font-size: .255rem;
		line-height: 1.3;
		color: #222222;
	}
	.elderly_bl_06 ul li img{
		width: 3.00rem;
		height: auto;
		display: block;
		margin: 0 auto;
	}
	.elderly_bl_06 ul{
		width: 6.80rem;
		margin: .20rem auto .48rem auto;
	}
	.office_table,
	.elderly_table{
		background-color: white;
		height: 5.75rem;
		margin: 0 auto;
		width: 7.40rem;
		border-collapse: collapse;
	    border-spacing: 0;
	}
	.office_table th,
	.elderly_table th{
		font-size: .255rem;
		color: #222222;
	    padding: .15rem;
	    font-weight: bold;
	    background: #f0f0f0;
	    border-top: .01rem dotted #ccc;
	    width: 100%;
	    display: block;
	}
	.office_table td,
	.elderly_table td{
		color: #222222;
		font-size: .255rem;
	    padding: .15rem;
	    background: #fff;
	    border-top: .01rem dotted #ccc;
	    border-bottom: .01rem dotted #ccc;
	    line-height: 1.3;
	    width: 100%;
	    display: block;
	}
	.elderly_bl_02,
	.elderly_bl_04,
	.elderly_bl_06,
	.office_bl_02,
	.office_bl_04,
	.hospital_bl_02{
		background-color: #e6e6e6;
	}
	/*end of office,elderly*/

	/*office*/
	.office_bl_03 .title{
		text-align: center;
	}
	.office_bl_03 .image_box{
		text-align: center;
		margin-bottom: .30rem;
		width: 7.40rem;
		margin: 0 auto;
	}
	.office_bl_03 .image_box p{
		text-align: left;
		margin: .10rem 0 .30rem .37rem;
		font-size: .255rem;
	}
	.office_bl_03 .description{
		width: 7.40rem;
	    margin: 0 auto;
	    font-size: .255rem;
	    line-height: 1.3;
	}
	.office_bl_03 .graybox{
		width: 7.40rem;
	    padding: .20rem;
	    background: #f4f4f4;
	    border: .01rem solid #c9c9c9;
	    margin: 0 auto .30rem auto;
	}
	.office_bl_03 ul li img{
		width: 1.60rem;
		height: auto;
		display: block;
		margin: 0 auto;
	}
	.layout_img_r{
	    display: block;
	    width: 100%;
	    margin: 0 auto;
	}
	.layout_img_r::after{
		margin-top: .25rem;
		margin-bottom: .25rem;
		content: " ";
		display: block;
		height: .05rem;
		background: white;
	}
	.layout_img_r:last-child:after{
	  	display: none;
	}
	.layout_img_r div.txt{
	    display: inline-block;
	    vertical-align: top;
	    width: 100%;
	    margin-bottom: .10rem;
	}
	.layout_img_r div.txt p{
	    font-size: .255rem;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 .05rem 0;
		margin: 0;
		border: none;
	}
	.layout_img_r img{
		width: 2.80rem;
		height: auto;
	}
	.office_bl_04 .layout_text,
	.office_bl_04 .layout_text_01{
		width: 7.40rem;
	    margin: 0 auto;
	}
	.office_bl_04 .layout_text h4{
		font-size: .28rem;
		color: #00b67f;
		line-height: 1;
		padding-bottom: .20rem;
	}
	.office_bl_04 .layout_text p{
		font-size: .255rem;
		color: #222222;
		line-height: 1.5;
		padding-bottom: .46rem;
	}
	.office_bl_04 .layout_text_01 p{
		font-size: .255rem;
		color: #222222;
		line-height: 1.5;
		padding-bottom: .20rem;
	}
	/*end of office*/

	/*hospital*/
	.service_hospital .tabs >ul{
		width: 4.60rem;
	}
	.service_hospital .elderly_bl_01,
	.service_hospital .elderly_bl_05{
		padding: 0 0 1.00rem 0;
	}
	.meal_offer_eg{
		width: 4.00rem;
		margin: 0 auto;
		padding: 1.00rem 0 .45rem 0;
	}
	.meal_offer_eg img{
		width: 100%;
		margin: 0 0 .10rem 0;
	}
	.meal_offer_eg label{
		font-size: .255rem;
		line-height: 1;
		color: #222222;
	}
	#hospital02 .elderly_bl_01{
		padding: 1.00rem 0 .45rem 0;
	}
	#hospital02 .mealoffer_ul{
		width: 6.40rem;
		margin: 0 auto .40rem auto;
	}
	.hospital02_mealoffer h4{
		width: 6.40rem;
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		margin: 0 auto .20rem auto;
	}
	#hospital02 .meal_offer_div{
		width: 6.40rem;
		margin: 0 auto;
	}
	#hospital02 .meal_offer_div img{
		width: 100%;
	}
	#hospital02 .meal_offer_div p{
		font-size: .255rem;
		line-height: 1;
		color: #222222;
		margin: .10rem 0 .30rem 0;
	}
	#hospital02 .mealoffer_ul_01{
		width: 6.40rem;
	}
	/*end of hospital*/

	/*service*/
	.service{
		width: 7.40rem;
		margin: 1.00rem auto;
	}
	.service_ul{
		margin: 0 0 1.00rem 0;
	}
	.service_ul li img{
		width: 5.80rem;
		height: auto;
		display: block;
		margin: 0 auto;
	}
	.service_three_major{
		margin: .60rem 0 0 0;
	}
	.service_three_major p{
		font-size: .255rem;
		line-height: 1.5;
		color: #222222;
	}
	.service_three_major label{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		display: block;
		margin: .45rem 0 .20rem 0;
	}
	.service .img_service_06{
		width: 7.40rem;
		height: auto;
		margin: .45rem auto 0 auto;
		display: block;
	}
	/*end of service*/

	/*annual_events*/
	.annual_events{
		width: 7.40rem;
		margin: 1.00rem auto;
	}
	.annual_events ul{
		width: 100%;
	}
	.events_div{
		width: 3.60rem;
		margin: 0 auto .40rem auto;
		text-align: center;
	}
	.events_div img{
		width: 100%;
		height: auto;
	}
	.events_div label{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		display: block;
		margin: .15rem 0 .10rem 0;
	}
	.events_div span{
		font-size: .255rem;
		line-height: 1;
		color: #222222;
	}
	/*end of annual_events*/

	/*event*/
	.event{
		padding: 1.00rem 0;
	}
	.event_div_01{
		background: #defcf3;
		padding: .70rem 0 0 0;
	}
	.event ul{
		width: 3.60rem;
		margin: 0 auto;
		padding: 0 0 .50rem 0;
	}
	.event ul li{
		margin: 0 0 .30rem 0;
	}
	.event ul img{
		width: 3.60rem;
		height: auto;
		margin: 0 auto;
		display: block;
	}
	.event p{
		font-size: .28rem;
		line-height: 1;
		color: #00b67f;
		font-weight: bold;
		padding: 0 0 .10rem 0;
		width: 3.60rem;
		margin: 0 auto;
	}
	.event_div_02{
		padding: .50rem 0;
	}
	.event ul.event_div_02_ul{
		display: block;
		width: 3.60rem;
	}
	.event ul.event_div_02_ul li{
		width: auto;
		display: inline-block;
		margin: 0 .30rem .30rem 0;
	}
	.event_div_02_ul .img_width{
		width: 1.90rem;
		height: auto;
	}
	.event ul.event_padding{
		padding: 0;
	}

	.dropmenu li ul li:first-child{
		height: unset;
	}
	#fade_in > li:hover ul{
		display: none;
	}
	
	.nav div.navin ul li ul li:not(:first-child){
		 width: 33%
	}
	.dropmenu li ul li a{
		font-size: 9px;
	}
	.dropmenu li ul li:not(:first-child) a{
		font-size: 12px;
		display: flex !important;
		flex-direction: column;
	}

	.nav_bg{
		height: 72px;
	}

	#fade_in.dropmenu > li:first-child:hover, #fade_in li:first-child ul li {
		/* background: unset; */
	}
	/*end of event*/
	ul.bxslider li img.bxpc{
		display: block;
		height: 240px;
	}

	.category-name{
        width: 100%;
		font-size: 15px;
        display: block;
        float: none;
	}
}
@media only print {
	* {
        color-adjust: exact!important;  
        -webkit-print-color-adjust: exact!important; 
         print-color-adjust: exact!important;
	}
	.table01 .white{
		color: white !important;
	}
}

.tabs > ul li a {
	padding: 25px 0;
}

.company_info {
	padding: 48px;
    background-color: white;
    border: 2px solid #b4b4b4;
}

@media (max-width: 768px) { 
	.contact-us-pc {
		width: 90%;
	  }
	  .tabs > ul li a {
		padding: 10px 0;
	  }
}

@media (max-width: 1366px) {
	.h3_common h3::before {
	  top: 0.6rem; 
	}
	.h3_common h3::after {
   bottom: -35px !important;
}
  }

  /* .h3_common h3:after {
	width: 100%;
	height:2px;
	background-color: #00b67f;
	display: inline-block;
	position: relative;
	content: "";
  }
  
  .h3_common h3::before {
	content: "";
	height: 10px;
	width: 10px;
	border-radius: 50%;
	background-color: #00b67f;
	display: inline-block;
	position: absolute;
	right: 0;
	top: 4rem;
  } */



.h3_common h3 {
    position: relative;
    display: inline-block; /* Đảm bảo giới hạn theo nội dung */
}
.h3_common h3::after {
	content: "";
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
	bottom: -15px;
	width: 100%;
	height: 3px;
	background: #00b67f;
	border-radius: 2.5px;
}


/***** title01 *****/
.title01 {
	position: relative;
	margin-top: 15px;
}

.title_area .title01 h2 {
	position: absolute;
	top: 16px;
	left: 0%;
	font-size: 55px;
	color: #FFF;
	font-weight: bold;
	font-family: Yu Mincho;
}

.footer_address, #menu-footer-menu1, #menu-footer-menu2, #menu-footer-menu3, #menu-footer-menu4{
	display: none !important;
}



/* start management */
.main_text {
	max-width: 1265px;
	margin: 0 auto;
	padding: 0 18px;
}

.row.clearfix.main_text {
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: flex-start;
    gap: 20px;
}

.grid_02 {
	flex: 0 0 calc(50% - 10px);
	max-width: calc(50% - 10px);
}

.padding_right {
	padding-right: 0;
}

.image-container {
	pointer-events: none;
}

.image-container img {
	pointer-events: none;
	width: 100%;
	height: auto;
	display: block;
}

@media (max-width: 768px) {
	.grid_sp_01 {
		flex: 0 0 100%;
		max-width: 100%;
	}
	.row.clearfix.main_text {
		flex-direction: column;
	}

	.row.clearfix.main_text p {
		font-size: 18px;
	}
	
}

#new_employee_training {
	max-width: 1200px;
	margin: 0 auto;
	padding: 40px 20px;
	text-align: center;
}

#new_employee_training p {
	font-size: 16px;
	line-height: 1.8;
	margin-bottom: 30px;
	text-align: left;
	max-width: 800px;
	margin-left: auto;
	margin-right: auto;
}

.img_new_employee_training {
	width: 100%;
	max-width: 52rem;
	height: auto;
	display: block;
	margin: 30px auto;
}

.new_employee_training_title {
	font-size: 24px;
	font-weight: bold;
	color: #333;
	margin: 40px 0 20px 0;
	text-align: center;
}

.new_employee_training_title img {
	width: 100%;
	max-width: 52rem;
	height: auto;
	display: block;
	margin: 20px auto 0;
}

@media (max-width: 768px) {
	#new_employee_training {
		padding: 20px 15px;
	}

	#new_employee_training p {
		font-size: 14px;
	}

	.new_employee_training_title {
		font-size: 20px;
	}
}

/* end management */