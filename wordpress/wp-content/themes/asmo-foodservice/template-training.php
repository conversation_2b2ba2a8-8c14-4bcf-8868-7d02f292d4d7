<?php
/*
 * Template Name: Tranning Template
 */
?>
<?php get_header(); ?>
<?php wp_enqueue_script("jquery-ui-tabs"); ?>
<div class="title_area clearfix">
	<div class="title_areain clearfix">
		<div class="title01">
			<?php if ($wp->request === 'inquiry' || $wp->request === 'confirm' || $wp->request === 'complete') { ?>
				<h2>お問い合わせ<span>inquiry</span></h2>
			<?php } elseif ($wp->request === 'fb.html') { ?>
				<h2>グループ会社のFacebook<span>facebook</span></h2>
			<?php } else { ?>
				<h2 id="tab-title"><?php the_title(); ?></h2>
			<?php } ?>
		</div>

		<div class="common_logo">
			<?php
			$featured_img_url = get_the_post_thumbnail_url(get_the_ID(), 'full');
			if ($featured_img_url) {
				echo '<img src="' . esc_url($featured_img_url) . '" alt="' . esc_attr(get_the_title()) . '" />';
			} else {
				echo '<img src="' . esc_url(get_template_directory_uri() . '/images/common_logo.png') . '" alt="株式会社アスモフードサービス" />';
			}
			?>
		</div>
	</div>
</div>

<div class="wrapper">
	<div id="management_tabs" class="tabs">
		<?php
		$tabs = [
			['id' => 'management', 'label' => '人材育成'],
			['id' => 'new_employee_training', 'label' => '新卒研修'],
			['id' => 'employ_training.html', 'label' => '従業員研修'],
			['id' => 'cakes_course.html', 'label' => '料理コンテスト'],
			['id' => 'contest.html', 'label' => '洋菓子講習会'],
		];
		?>
		<ul class="row clearfix">
			<?php foreach ($tabs as $i => $tab): ?>
				<li class="grid_05<?php if ($i === 0) echo ' ui-tabs-active'; ?>">
					<a href="#<?php echo esc_attr($tab['id']); ?>"><?php echo esc_html($tab['label']); ?></a>
				</li>
			<?php endforeach; ?>
		</ul>

		<div id="management" class="tabs_common">
			<div class="header_common">
				<div class="h3_common">
					<h3>スペシャリストの育成</h3>
				</div>
				<p class="text_p">お客様の立場に立って考えることのできる人材を育成し、真心のこもったサービスを目指しております。</p>
			</div>
			<ul class="row clearfix main_text">
				<div class="grid_02 grid_sp_01 padding_right">
					<p>
						お客様に良質なサービスを提供するためには、それに携わる人材の技術向上を欠か<br>すことはできません。定期的に行う研修など、独自の社員教育システムにより、<br> 人材育成に努めております。
					</p>
				</div>
				<li class="grid_02 grid_sp_01" style="pointer-events: none;">
					<img src="<?php echo esc_url(get_template_directory_uri() . '/images/management/training/training-001.jpg'); ?>" alt="" style="pointer-events: none;">
				</li>
			</ul>
		</div>

		<div id="new_employee_training" class="tabs_common">
			<p>「マナー・モラル」「コミュニケーション」など社会人としての基礎や衛生管理、包丁の研ぎ方から食材の切り方や下処理の仕方など調理の基礎を、2日間にわたる研修会で学び、会社組織の一員として、今後行っていく業務の意義や方向性を身につけることを目的にしております。</p>
			<img class="img_new_employee_training" src="<?php echo esc_url(get_template_directory_uri() . '/images/management/training/training12.jpg'); ?>" alt="">
			<div class="new_employee_training_title">
				一年の流れ
				<img src="[template_uri]/images/1年の流れ.jpg'); ?>" alt="">
			</div>
		</div>

		<div id="employee_training" class="tabs_common">
			<p>各事業所から従業員が集い、顧客満足度の向上、衛生管理、スチコン活用法など、毎回テーマを決め、研修を行っております。</p>
			<div class="d-flex tabs_common-img">
				<img class="img_employee_training_01" src="<?php echo esc_url(get_template_directory_uri() . '/images/management/training/training21.jpg'); ?>" alt="">
				<img class="img_employee_training_02" src="<?php echo esc_url(get_template_directory_uri() . '/images/management/training/training-003.jpg'); ?>" alt="">
			</div>
		</div>

		<div id="contest" class="tabs_common">
			<ul class="row clearfix">
				<li class="grid_02 grid_sp_01 padding_right">
					<p>事業所で培った従業員個々の知識・技術・経験を十分に発揮する場として、「料理コンテスト」を行っております。</p>
					<p>最優秀賞チームの献立はイベント食として提供しており、お客様にも喜んで頂き、従業員の士気も自然と高まります。</p>
				</li>
				<li class="grid_02 grid_sp_01" style="pointer-events: none;">
					<img src="<?php echo esc_url(get_template_directory_uri() . '/images/management/training/training31.jpg'); ?>" alt="">
				</li>
			</ul>
			<ul class="ul_contest_img row clearfix">
				<?php
				$contest_imgs = ['training32a.jpg', 'training32b.jpg', 'training32c.jpg'];
				foreach ($contest_imgs as $img): ?>
					<li class="grid_03 grid_sp_02" style="pointer-events: none;">
						<img src="<?php echo esc_url(get_template_directory_uri() . '/images/management/training/' . $img); ?>" alt="">
					</li>
				<?php endforeach; ?>
			</ul>
		</div>

		<div id="cakes_course" class="tabs_common">
			<p>ル・スリール ダンジュ（Le sourire d’ange）オーナーシェフであるパティシエが、年3回本格的なケーキの作り方を直接指導しております。</p>
			<div id="main">
				<div class="container container-fluid">
					<div id="wrapper">
						<button id="prev" onclick="prev()">
							<span class="arrow"></span>
						</button>
						<div id="image-container">
							<div id="image-carousel">
								<?php
								$cake_images = ["img_cake_course_03.png", "img_cake_course_04.png", "img_cake_course_05.png", "img_cake_course_06.png", "img_cake_course_07.png", "ASMO FOOD.png", "case_01.png", "case_02.png", "case_03.png",];
								$img_width = 320;
								$img_height = 240;
								foreach ($cake_images as $img) {
									echo '<img src="' . esc_url(get_template_directory_uri() . '/images/management/' . $img) . '" alt="" width="' . $img_width . '" height="' . $img_height . '" style="object-fit:cover; aspect-ratio:4/3;">';
								}
								?>
							</div>
						</div>
						<button id="next" onclick="next()">
							<span class="arrow"></span>
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<?php get_footer(); ?>
	<!-- wrapper end -->
</div>
<script>
	(function() {
		const tabList = document.querySelectorAll("#management_tabs ul li");
		const tabContents = document.querySelectorAll(".tabs_common");
		const tabTitle = document.getElementById("tab-title");
		const tabSlugs = ["training", "new_employee_training", "employee_training", "contest", "cakes_course"];
		const tabUrls = tabSlugs.map(s => `/management/${s}.html`);
		const carousel = document.getElementById('image-carousel');
		const images = carousel.querySelectorAll('img');
		let tabIdx = tabUrls.findIndex(url => location.pathname.endsWith(url));
		if (tabIdx < 0) tabIdx = +localStorage.getItem("asmo_training_active_tab") || 0;

		function activateTab(i) {
			tabList.forEach((t, j) => t.classList.toggle("ui-tabs-active", j === i));
			tabContents.forEach((c, j) => c.style.display = j === i ? "block" : "none");
			if (tabTitle) tabTitle.textContent = tabList[i].querySelector("a").textContent;
			localStorage.setItem("asmo_training_active_tab", i);
		}
		document.querySelector("#management_tabs ul").onclick = e => {
			const li = e.target.closest("li");
			if (!li) return;
			e.preventDefault();
			const i = [...tabList].indexOf(li);
			if (i < 0) return;
			activateTab(i);
			history.replaceState(null, '', tabUrls[i]);
		};
		if (tabList[tabIdx]) activateTab(tabIdx);

		let idx = 0;

		function imgsPerSlide() {
			return window.innerWidth <= 768 ? 1 : 3;
		}

		function updateCarousel() {
			const n = imgsPerSlide(),
				max = images.length - n;
			if (idx > max) idx = max < 0 ? 0 : max;
			const w = 100 / n;
			images.forEach((img, i) => {
				img.style.display = (i >= idx && i < idx + n) ? 'inline-block' : 'none';
				img.style.width = w + '%';
			});
			carousel.style.width = '100%';
			carousel.style.transform = 'none';
		}
		window.next = () => {
			if (idx < images.length - imgsPerSlide()) {
				idx++;
				updateCarousel();
			}
		};
		window.prev = () => {
			if (idx > 0) {
				idx--;
				updateCarousel();
			}
		};
		addEventListener('resize', updateCarousel);
		updateCarousel();
	})();
</script>

<style>
	.container-fluid {
		background: url("<?php echo get_template_directory_uri(); ?>/images/bg_cake_course.jpg") no-repeat -43px -32px;
		background-size: cover;
		background-position: center;
		padding: 0;
		max-width: 1170px;
	}

	.main_text {
		width: 1447px !important;
		display: flex;
		align-items: center;
		height: 100%;
	}

	.contact_area {
		background-color: #dcf3fe;
	}

	.ul_cake_course {
		width: 1201px !important;
		margin: 25px auto !important;
	}

	.ul_cake_course img {
		width: 100%;
		height: auto;
		object-fit: cover;
	}

	.new_employee_training_title {
		font-size: 24px;
		font-weight: bold;
		width: 100%;
		color: #222222;
		margin-top: 20px;
		text-align: center;
	}
	.new_employee_training_title img {
		width: 100%;
		height: auto;
		max-width: 100%;
		object-fit: cover;
		margin-top: 20px !important;
	}

	@media (max-width: 768px) {
		.ul_cake_course {
			width: 100% !important;
			flex-direction: column !important;
			gap: 16px !important;
			align-items: center !important;
			height: 16rem;
		}

		.ul_cake_course li {
			width: 100% !important;
			max-width: 320px;
			display: flex !important;
			justify-content: center !important;
		}

		.ul_cake_course img {
			width: 100%;
			height: 240px !important;
			min-height: 240px !important;
			max-width: 100%;
			object-fit: cover;
			aspect-ratio: 4/3;
		}
	}

	.text_p {
		font-size: 18px;
		line-height: 1.5;
		color: #222222;
		padding: 0 0 30px 0;
		font-weight: normal !important;
	}

	#management_tabs p {
		font-weight: normal !important;
	}
</style>